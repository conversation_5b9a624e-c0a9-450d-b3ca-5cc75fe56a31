    
    async loadDataFromFiles(meshFile, planFile) {
        try {
            document.getElementById('loading').classList.remove('hidden');
            
            // Load mesh data
            const meshResponse = await fetch(meshFile);
            const meshData = await meshResponse.json();
            this.createVertebraeFromMeshData(meshData);
            
            // Load plan data
            const planResponse = await fetch(planFile);
            const planData = await planResponse.json();
            this.puncturePoints = planData;
            
            console.log('Data loaded successfully');
            
        } catch (error) {
            console.error('Failed to load data:', error);
            alert('Failed to load visualization data: ' + error.message);
        } finally {
            document.getElementById('loading').classList.add('hidden');
        }
    }
    
    createVertebraeFromMeshData(meshData) {
        Object.keys(meshData).forEach(label => {
            if (this.colors[label]) {
                const data = meshData[label];

                // Create geometry
                const geometry = new THREE.BufferGeometry();
                geometry.setAttribute('position', new THREE.Float32BufferAttribute(data.vertices, 3));
                if (data.faces) {
                    geometry.setIndex(data.faces);
                }
                geometry.computeVertexNormals();

                // Create material
                const material = new THREE.MeshLambertMaterial({
                    color: this.colors[label],
                    transparent: true,
                    opacity: 0.7,
                    side: THREE.DoubleSide
                });

                // Create mesh
                const mesh = new THREE.Mesh(geometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;

                this.vertebraMeshes[label] = mesh;
                this.scene.add(mesh);
                
                console.log(`Created ${label} vertebra with ${data.vertex_count} vertices`);
            }
        });
    }
    
    toggleVertebra(vertebra, visible) {
        if (this.vertebraMeshes[vertebra]) {
            this.vertebraMeshes[vertebra].visible = visible;
        }
    }
    
    setOpacity(opacity) {
        Object.values(this.vertebraMeshes).forEach(mesh => {
            mesh.material.opacity = opacity;
        });
    }
    
    showPuncturePlan(level) {
        // Clear existing puncture points
        this.clearPuncturePoints();
        
        if (!level || !this.puncturePoints[level]) {
            this.currentPlan = null;
            return;
        }
        
        this.currentPlan = level;
        const plan = this.puncturePoints[level];
        
        // Create target point A
        if (plan.target_point_A) {
            const targetGeometry = new THREE.SphereGeometry(0.5, 16, 16);
            const targetMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
            const targetMesh = new THREE.Mesh(targetGeometry, targetMaterial);
            targetMesh.position.set(...plan.target_point_A);
            targetMesh.name = 'target_point_A';
            this.scene.add(targetMesh);
        }

        // Create B points
        const bPointGeometry = new THREE.SphereGeometry(0.3, 12, 12);
        const bPointMaterial = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
        
        ['upper_vertebra', 'lower_vertebra'].forEach(vertebra => {
            const vertebraData = plan[vertebra];
            if (vertebraData) {
                ['left_articular_tip_B', 'right_articular_tip_B'].forEach(tip => {
                    if (vertebraData[tip]) {
                        const bMesh = new THREE.Mesh(bPointGeometry, bPointMaterial);
                        bMesh.position.set(...vertebraData[tip]);
                        bMesh.name = `${vertebra}_${tip}`;
                        this.scene.add(bMesh);
                    }
                });
            }
        });
        
        // Create puncture path lines
        if (plan.target_point_A) {
            const lineMaterial = new THREE.LineBasicMaterial({ 
                color: 0xff0000, 
                transparent: true, 
                opacity: 0.6 
            });
            
            ['upper_vertebra', 'lower_vertebra'].forEach(vertebra => {
                const vertebraData = plan[vertebra];
                if (vertebraData) {
                    ['left_articular_tip_B', 'right_articular_tip_B'].forEach(tip => {
                        if (vertebraData[tip]) {
                            const points = [
                                new THREE.Vector3(...vertebraData[tip]),
                                new THREE.Vector3(...plan.target_point_A)
                            ];
                            const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
                            const line = new THREE.Line(lineGeometry, lineMaterial);
                            line.name = `path_${vertebra}_${tip}`;
                            this.scene.add(line);
                        }
                    });
                }
            });
        }
        
        console.log(`Showing puncture plan for ${level}`);
    }
    
    clearPuncturePoints() {
        const objectsToRemove = [];
        this.scene.traverse((object) => {
            if (object.name && (
                object.name.includes('target_point') ||
                object.name.includes('articular_tip') ||
                object.name.includes('path_')
            )) {
                objectsToRemove.push(object);
            }
        });
        
        objectsToRemove.forEach(object => {
            this.scene.remove(object);
        });
    }
    
    resetView() {
        this.camera.position.set(0, 0, 200);
        this.controls.reset();
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
}

// Export for use
window.SpineViewer = SpineViewer;
