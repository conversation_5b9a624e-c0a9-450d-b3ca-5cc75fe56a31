#!/usr/bin/env python3
"""
验证坐标修复的脚本
"""

import os
import json
import numpy as np
from puncture_planning import Puncture<PERSON><PERSON><PERSON>

def verify_coordinate_fix():
    """验证坐标修复是否成功"""
    
    print("=" * 80)
    print("Verifying Coordinate Fix")
    print("=" * 80)
    
    # 检查分割文件
    segmentation_file = 'spine_pelvis_output_final.nii.gz'
    if not os.path.exists(segmentation_file):
        print(f"Error: Segmentation file not found: {segmentation_file}")
        return False
    
    # 创建穿刺规划器
    planner = PuncturePlanner(segmentation_file)
    
    # 1. 检查L5网格中心
    print("\n1. Checking L5 mesh center...")
    l5_vertices, l5_faces = planner.generate_vertebra_surface(24, simplify_factor=10)
    
    if l5_vertices is None:
        print("Error: Could not generate L5 mesh")
        return False
    
    l5_vertices_scaled = l5_vertices / 10.0
    l5_mesh_center = l5_vertices_scaled.mean(axis=0)
    print(f"   L5 mesh center: {l5_mesh_center}")
    
    # 2. 检查修复后的L5中心计算
    print("\n2. Checking fixed L5 center calculation...")
    
    # 导入修复后的函数
    import sys
    sys.path.append('.')
    from complete_threejs_demo import get_l5_center
    
    l5_center_fixed = get_l5_center(planner)
    print(f"   Fixed L5 center: {l5_center_fixed}")
    
    # 3. 比较两个结果
    print("\n3. Comparing results...")
    difference = np.array(l5_mesh_center) - np.array(l5_center_fixed)
    distance = np.linalg.norm(difference)
    
    print(f"   Difference: {difference}")
    print(f"   Distance: {distance:.6f}")
    
    # 4. 检查穿刺点位置
    print("\n4. Checking puncture point positions...")
    
    # 规划穿刺
    l4l5_plan = planner.plan_puncture_path('L4L5')
    l5s1_plan = planner.plan_puncture_path('L5S1')
    
    if l4l5_plan:
        target_scaled = [c / 10.0 for c in l4l5_plan['target_point_A']]
        distance_to_l5 = np.linalg.norm(np.array(target_scaled) - np.array(l5_center_fixed))
        print(f"   L4L5 target: {target_scaled}")
        print(f"   Distance to L5 center: {distance_to_l5:.2f}")
        
        # 检查Z坐标是否在合理范围内
        l5_z_range = [l5_vertices_scaled[:, 2].min(), l5_vertices_scaled[:, 2].max()]
        z_reasonable = l5_z_range[0] - 5 <= target_scaled[2] <= l5_z_range[1] + 5
        print(f"   Z coordinate reasonable: {z_reasonable} (target: {target_scaled[2]:.2f}, L5 range: {l5_z_range[0]:.2f} to {l5_z_range[1]:.2f})")
    
    if l5s1_plan:
        target_scaled = [c / 10.0 for c in l5s1_plan['target_point_A']]
        distance_to_l5 = np.linalg.norm(np.array(target_scaled) - np.array(l5_center_fixed))
        print(f"   L5S1 target: {target_scaled}")
        print(f"   Distance to L5 center: {distance_to_l5:.2f}")
        
        # 检查Z坐标是否在合理范围内
        z_reasonable = l5_z_range[0] - 5 <= target_scaled[2] <= l5_z_range[1] + 5
        print(f"   Z coordinate reasonable: {z_reasonable} (target: {target_scaled[2]:.2f}, L5 range: {l5_z_range[0]:.2f} to {l5_z_range[1]:.2f})")
    
    # 5. 总结
    print("\n5. Summary...")
    
    success = True
    
    # 检查L5中心计算是否一致
    if distance > 0.001:  # 允许很小的数值误差
        print("   ❌ L5 center calculation is inconsistent")
        success = False
    else:
        print("   ✅ L5 center calculation is consistent")
    
    # 检查穿刺点是否在合理位置
    if l4l5_plan and l5s1_plan:
        l4l5_target_scaled = [c / 10.0 for c in l4l5_plan['target_point_A']]
        l5s1_target_scaled = [c / 10.0 for c in l5s1_plan['target_point_A']]
        
        l4l5_reasonable = l5_z_range[0] - 5 <= l4l5_target_scaled[2] <= l5_z_range[1] + 5
        l5s1_reasonable = l5_z_range[0] - 5 <= l5s1_target_scaled[2] <= l5_z_range[1] + 5
        
        if l4l5_reasonable and l5s1_reasonable:
            print("   ✅ Puncture points are in reasonable positions")
        else:
            print("   ❌ Puncture points are not in reasonable positions")
            success = False
    else:
        print("   ❌ Could not generate puncture plans")
        success = False
    
    if success:
        print("\n🎉 Coordinate fix verification PASSED!")
        print("   The visualization should now show puncture points and vertebrae in correct relative positions.")
    else:
        print("\n❌ Coordinate fix verification FAILED!")
        print("   There are still issues with coordinate consistency.")
    
    return success

if __name__ == "__main__":
    verify_coordinate_fix()
