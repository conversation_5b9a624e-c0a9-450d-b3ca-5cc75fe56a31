<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spine Puncture Planning - 3D Visualization</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            min-width: 250px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 1000;
        }

        #controls h3 {
            margin-top: 0;
            color: #4ecdc4;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group input[type="checkbox"] {
            margin-right: 8px;
        }

        .control-group input[type="range"] {
            width: 100%;
        }

        .control-group select, .control-group button {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 4px;
            background: #333;
            color: white;
        }

        .control-group button {
            background: #4ecdc4;
            cursor: pointer;
            margin-top: 5px;
        }

        .control-group button:hover {
            background: #45b7d1;
        }

        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            max-width: 300px;
        }

        .legend {
            margin-top: 10px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 2000;
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <h3>Loading 3D Visualization...</h3>
            <p>Please wait while the spine model is being rendered.</p>
        </div>

        <div id="controls">
            <h3>Spine Puncture Planning</h3>

            <div class="control-group">
                <label>Show Vertebrae:</label>
                <div>
                    <input type="checkbox" id="showL3" checked> L3
                    <input type="checkbox" id="showL4" checked> L4
                    <input type="checkbox" id="showL5" checked> L5
                    <input type="checkbox" id="showS1" checked> S1
                </div>
            </div>

            <div class="control-group">
                <label for="punctureLevel">Puncture Planning:</label>
                <select id="punctureLevel">
                    <option value="">Select Level</option>
                    <option value="L4L5">L4L5</option>
                    <option value="L5S1">L5S1</option>
                </select>
            </div>

            <div class="control-group">
                <label for="opacity">Vertebra Opacity:</label>
                <input type="range" id="opacity" min="0.1" max="1.0" step="0.1" value="0.7">
                <span id="opacityValue">0.7</span>
            </div>

            <div class="control-group">
                <button id="resetView">Reset View</button>
            </div>
        </div>

        <div id="info">
            <h4>3D Spine Puncture Planning</h4>
            <p>Use mouse to rotate, scroll to zoom</p>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff6b6b;"></div>
                    <span>L3 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ecdc4;"></div>
                    <span>L4 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45b7d1;"></div>
                    <span>L5 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f9ca24;"></div>
                    <span>S1 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff0000;"></div>
                    <span>Target Point A</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #00ff00;"></div>
                    <span>Articular Process B</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- OrbitControls -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>

    <!-- Main Script -->
    <script src="spine_viewer.js"></script>

    <!-- Initialize with data -->
    <script>
        // Load data when page loads
        window.addEventListener('load', function() {
            const viewer = new SpineViewer();
            viewer.loadDataFromFiles('vertebrae_meshes.json', 'puncture_plans.json');
        });
    </script>
</body>
</html>