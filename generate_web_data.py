#!/usr/bin/env python3
"""
生成Web展示所需的3D网格数据

这个脚本将NIfTI分割数据转换为Three.js可以使用的网格格式，
并生成穿刺规划的JSON数据。
"""

import os
import json
import nibabel as nib
import numpy as np
from skimage import measure
from scipy import ndimage
import argparse

class WebDataGenerator:
    def __init__(self):
        self.vertebra_labels = {
            22: 'L3',
            23: 'L4', 
            24: 'L5',
            27: 'S1'
        }
        
    def generate_mesh_from_segmentation(self, segmentation_path, output_dir):
        """
        从分割数据生成3D网格
        
        Args:
            segmentation_path: 分割文件路径
            output_dir: 输出目录
        """
        print(f"正在处理分割文件: {segmentation_path}")
        
        # 读取分割数据
        seg_img = nib.load(segmentation_path)
        seg_data = seg_img.get_fdata()
        affine = seg_img.affine
        
        print(f"分割数据形状: {seg_data.shape}")
        unique_labels = np.unique(seg_data)
        print(f"找到的标签: {sorted(unique_labels)}")
        
        mesh_data = {}
        
        for label_id, vertebra_name in self.vertebra_labels.items():
            if label_id in unique_labels:
                print(f"正在生成 {vertebra_name} 的网格...")
                
                # 提取椎体mask
                vertebra_mask = seg_data == label_id
                
                if np.sum(vertebra_mask) == 0:
                    print(f"警告: {vertebra_name} 没有体素数据")
                    continue
                
                # 平滑处理
                vertebra_mask = ndimage.binary_closing(vertebra_mask, iterations=2)
                vertebra_mask = ndimage.binary_opening(vertebra_mask, iterations=1)
                
                # 使用marching cubes生成网格
                try:
                    vertices, faces, normals, values = measure.marching_cubes(
                        vertebra_mask.astype(np.float32), 
                        level=0.5,
                        spacing=np.abs(np.diag(affine)[:3])
                    )
                    
                    # 应用仿射变换
                    vertices_homogeneous = np.column_stack([vertices, np.ones(len(vertices))])
                    vertices_world = (affine @ vertices_homogeneous.T).T[:, :3]
                    
                    # 缩放到合适的显示范围
                    vertices_world = vertices_world / 10.0  # 缩放因子
                    
                    # 简化网格（减少顶点数量）
                    vertices_simplified, faces_simplified = self.simplify_mesh(
                        vertices_world, faces, target_faces=1000
                    )
                    
                    mesh_data[vertebra_name] = {
                        'vertices': vertices_simplified.flatten().tolist(),
                        'faces': faces_simplified.flatten().tolist(),
                        'vertex_count': len(vertices_simplified),
                        'face_count': len(faces_simplified)
                    }
                    
                    print(f"  {vertebra_name}: {len(vertices_simplified)} 顶点, {len(faces_simplified)} 面")
                    
                except Exception as e:
                    print(f"生成 {vertebra_name} 网格时出错: {e}")
                    continue
            else:
                print(f"未找到 {vertebra_name} (标签 {label_id})")
        
        # 保存网格数据
        mesh_output_path = os.path.join(output_dir, 'vertebrae_meshes.json')
        with open(mesh_output_path, 'w') as f:
            json.dump(mesh_data, f, indent=2)
        
        print(f"网格数据已保存到: {mesh_output_path}")
        return mesh_data
    
    def simplify_mesh(self, vertices, faces, target_faces=1000):
        """
        简化网格以减少顶点数量
        """
        if len(faces) <= target_faces:
            return vertices, faces
        
        # 简单的网格简化：每隔几个面取一个
        step = max(1, len(faces) // target_faces)
        simplified_faces = faces[::step]
        
        # 找到使用的顶点
        used_vertices = np.unique(simplified_faces.flatten())
        
        # 重新映射顶点索引
        vertex_map = {old_idx: new_idx for new_idx, old_idx in enumerate(used_vertices)}
        simplified_vertices = vertices[used_vertices]
        
        # 更新面的顶点索引
        for i in range(len(simplified_faces)):
            for j in range(3):
                simplified_faces[i][j] = vertex_map[simplified_faces[i][j]]
        
        return simplified_vertices, simplified_faces
    
    def convert_puncture_plan_to_web_format(self, plan_files, output_dir):
        """
        转换穿刺规划数据为Web格式
        
        Args:
            plan_files: 穿刺规划文件列表
            output_dir: 输出目录
        """
        web_plan_data = {}
        
        for plan_file in plan_files:
            if os.path.exists(plan_file):
                print(f"正在处理穿刺规划文件: {plan_file}")
                
                with open(plan_file, 'r', encoding='utf-8') as f:
                    plan_data = json.load(f)
                
                # 确定椎间隙类型
                if 'l4l5' in plan_file.lower():
                    level = 'L4L5'
                elif 'l5s1' in plan_file.lower():
                    level = 'L5S1'
                else:
                    level = plan_data.get('target_level', 'Unknown')
                
                # 转换坐标系（缩放到与网格匹配）
                converted_plan = self.convert_coordinates(plan_data)
                web_plan_data[level] = converted_plan
        
        # 保存Web格式的规划数据
        web_plan_path = os.path.join(output_dir, 'puncture_plans.json')
        with open(web_plan_path, 'w', encoding='utf-8') as f:
            json.dump(web_plan_data, f, indent=2, ensure_ascii=False)
        
        print(f"Web格式穿刺规划已保存到: {web_plan_path}")
        return web_plan_data
    
    def convert_coordinates(self, plan_data):
        """
        转换坐标系以匹配Web显示
        """
        def convert_point(point):
            if point is None:
                return None
            return [p / 10.0 for p in point]  # 应用相同的缩放因子
        
        converted = {}
        
        for key, value in plan_data.items():
            if key == 'target_point_A':
                converted[key] = convert_point(value)
            elif isinstance(value, dict):
                converted[key] = {}
                for sub_key, sub_value in value.items():
                    converted[key][sub_key] = convert_point(sub_value)
            else:
                converted[key] = value
        
        return converted
    
    def generate_complete_web_data(self, segmentation_path, plan_files, output_dir):
        """
        生成完整的Web展示数据
        
        Args:
            segmentation_path: 分割文件路径
            plan_files: 穿刺规划文件列表
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        
        print("=" * 60)
        print("生成Web展示数据")
        print("=" * 60)
        
        # 生成网格数据
        mesh_data = self.generate_mesh_from_segmentation(segmentation_path, output_dir)
        
        # 转换穿刺规划数据
        if plan_files:
            plan_data = self.convert_puncture_plan_to_web_format(plan_files, output_dir)
        else:
            plan_data = {}
        
        # 生成配置文件
        config = {
            'mesh_file': 'vertebrae_meshes.json',
            'plan_file': 'puncture_plans.json',
            'vertebrae': list(mesh_data.keys()),
            'plans': list(plan_data.keys()) if plan_data else [],
            'generated_at': str(np.datetime64('now'))
        }
        
        config_path = os.path.join(output_dir, 'config.json')
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"配置文件已保存到: {config_path}")
        
        print("\n" + "=" * 60)
        print("Web数据生成完成！")
        print(f"输出目录: {output_dir}")
        print("生成的文件:")
        print(f"  - vertebrae_meshes.json (椎体网格数据)")
        print(f"  - puncture_plans.json (穿刺规划数据)")
        print(f"  - config.json (配置文件)")
        print("=" * 60)

def main():
    parser = argparse.ArgumentParser(description="生成Web展示所需的3D数据")
    parser.add_argument("segmentation", help="分割文件路径")
    parser.add_argument("-p", "--plans", nargs="*", help="穿刺规划文件路径")
    parser.add_argument("-o", "--output", default="web_data", help="输出目录")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.segmentation):
        print(f"错误: 分割文件不存在: {args.segmentation}")
        return
    
    generator = WebDataGenerator()
    generator.generate_complete_web_data(
        args.segmentation,
        args.plans or [],
        args.output
    )

if __name__ == "__main__":
    main()
