#!/usr/bin/env python3
"""
完整的Three.js可视化演示

这个脚本演示了完整的穿刺规划到Three.js可视化的工作流程。
"""

import os
import sys
import argparse
from puncture_planning import PuncturePlanner

def main():
    parser = argparse.ArgumentParser(description="Complete Three.js visualization demo")
    parser.add_argument("--mode", choices=['embedded', 'server', 'both'], default='both',
                       help="Visualization mode (default: both)")
    parser.add_argument("--no-browser", action="store_true", 
                       help="Don't open browser automatically")
    parser.add_argument("--output-dir", default="threejs_output",
                       help="Output directory for server mode")
    
    args = parser.parse_args()
    
    # 检查分割文件
    segmentation_file = 'spine_pelvis_output_final.nii.gz'
    if not os.path.exists(segmentation_file):
        print(f"Error: Segmentation file not found: {segmentation_file}")
        print("Please run the spine segmentation workflow first")
        return
    
    print("=" * 70)
    print("Complete Three.js Spine Visualization Demo")
    print("=" * 70)
    print(f"Using segmentation file: {segmentation_file}")
    print(f"Mode: {args.mode}")
    
    # 创建穿刺规划器
    planner = PuncturePlanner(segmentation_file)
    
    # 执行穿刺规划
    print("\n1. Performing Puncture Planning")
    print("-" * 50)
    
    plans = {}
    
    # L4/L5椎间隙规划
    print("Planning L4/L5 intervertebral puncture...")
    l4l5_plan = planner.plan_puncture_path('L4L5')
    if l4l5_plan:
        plans['L4L5'] = l4l5_plan
        print(f"  ✓ L4/L5 Target Point A: {l4l5_plan['target_point_A']}")
    else:
        print("  ✗ L4/L5 planning failed")
    
    # L5/S1椎间隙规划
    print("Planning L5/S1 intervertebral puncture...")
    l5s1_plan = planner.plan_puncture_path('L5S1')
    if l5s1_plan:
        plans['L5S1'] = l5s1_plan
        print(f"  ✓ L5/S1 Target Point A: {l5s1_plan['target_point_A']}")
    else:
        print("  ✗ L5/S1 planning failed")
    
    if not plans:
        print("No valid puncture plans generated. Exiting.")
        return
    
    print(f"\n✓ Successfully planned {len(plans)} puncture levels")
    
    # 生成可视化
    if args.mode in ['embedded', 'both']:
        print("\n2. Creating Embedded Three.js Visualization")
        print("-" * 50)
        create_embedded_visualization(planner, plans, not args.no_browser)
    
    if args.mode in ['server', 'both']:
        print("\n3. Creating Server-based Three.js Visualization")
        print("-" * 50)
        create_server_visualization(planner, plans, args.output_dir, not args.no_browser)
    
    # 显示使用说明
    print("\n4. Usage Instructions")
    print("-" * 50)
    print("The generated visualizations include:")
    print("  • Interactive 3D spine model with vertebrae")
    print("  • Puncture planning points (A and B points)")
    print("  • Control panel for toggling vertebrae visibility")
    print("  • Opacity control for vertebrae")
    print("  • Puncture plan selection dropdown")
    print("  • Mouse controls: drag to rotate, scroll to zoom")
    
    if args.mode in ['embedded', 'both']:
        print("\nEmbedded version:")
        print("  - Single HTML file with all data embedded")
        print("  - No server required, works offline")
        print("  - Larger file size but more portable")
    
    if args.mode in ['server', 'both']:
        print("\nServer version:")
        print("  - Separate files for better organization")
        print("  - Requires local server to avoid CORS issues")
        print("  - Smaller individual files, better for development")

def get_l5_center(planner):
    """获取L5椎骨的中心位置（世界坐标）- 使用与网格生成相同的方法"""
    try:
        # 方法1：使用网格中心（推荐）
        print("Calculating L5 center using mesh generation method...")
        l5_vertices, l5_faces = planner.generate_vertebra_surface(24, simplify_factor=10)

        if l5_vertices is not None:
            # 应用与网格数据相同的缩放因子
            l5_vertices_scaled = l5_vertices / 10.0
            l5_center_scaled = l5_vertices_scaled.mean(axis=0)

            print(f"L5 center from mesh (scaled): {l5_center_scaled}")
            return l5_center_scaled.tolist()

        # 方法2：备用方法 - 使用质心但修正坐标系
        print("Fallback: Using centroid method...")
        l5_mask = planner.extract_vertebra_mask(24)

        if not l5_mask.any():
            print("Warning: L5 vertebra not found")
            return [0, 0, -100]  # 默认中心

        # 计算L5质心（体素坐标）
        l5_centroid_voxel = planner.find_vertebra_centroid(l5_mask)

        if l5_centroid_voxel is None:
            print("Warning: Could not calculate L5 centroid")
            return [0, 0, -100]  # 默认中心

        # 使用与marching cubes相同的方法转换坐标
        import numpy as np
        from skimage import measure

        # 模拟marching cubes的坐标变换
        spacing = np.abs(np.diag(planner.affine)[:3])

        # 将体素坐标转换为marching cubes坐标
        mc_coords = l5_centroid_voxel * spacing

        # 应用仿射变换
        homogeneous = np.append(mc_coords, 1)
        l5_center_world = planner.affine @ homogeneous
        l5_center_world = l5_center_world[:3]

        # 应用与网格数据相同的缩放因子
        l5_center_scaled = l5_center_world / 10.0

        print(f"L5 center (world): {l5_center_world}")
        print(f"L5 center (scaled): {l5_center_scaled}")

        return l5_center_scaled.tolist()

    except Exception as e:
        print(f"Error calculating L5 center: {e}")
        return [0, 0, -100]  # 默认中心

def create_embedded_visualization(planner, plans, open_browser=True):
    """创建嵌入式可视化"""
    try:
        # 计算L5中心作为渲染中心
        print("Calculating L5 center for rendering...")
        l5_center = get_l5_center(planner)

        # 生成简化的网格数据
        print("Generating simplified mesh data...")
        mesh_data = planner.generate_web_mesh_data(simplify_factor=10)

        # 转换规划数据
        web_plans = {}
        for level, plan in plans.items():
            web_plan = planner.convert_plan_to_web_format(plan)
            if web_plan:
                web_plans[level] = web_plan

        # 创建嵌入式HTML
        output_file = "spine_visualization_embedded.html"
        create_embedded_html(output_file, mesh_data, web_plans, l5_center)

        print(f"✓ Embedded visualization created: {output_file}")
        print(f"  File size: {os.path.getsize(output_file):,} bytes")
        print(f"  Rendering center set to L5: {l5_center}")

        if open_browser:
            import webbrowser
            file_url = f"file://{os.path.abspath(output_file)}"
            webbrowser.open(file_url)
            print(f"  Opening browser: {file_url}")

    except Exception as e:
        print(f"✗ Failed to create embedded visualization: {e}")

def create_server_visualization(planner, plans, output_dir, open_browser=True):
    """创建服务器版本可视化"""
    try:
        print(f"Generating server-based visualization to: {output_dir}")

        # 计算L5中心作为渲染中心
        print("Calculating L5 center for rendering...")
        l5_center = get_l5_center(planner)

        planner.generate_threejs_webpage(
            puncture_plans=plans,
            output_dir=output_dir,
            auto_open=False  # 我们稍后手动打开
        )

        # 修改生成的HTML文件以使用L5中心
        update_server_html_with_l5_center(output_dir, l5_center)

        print(f"✓ Server visualization created in: {output_dir}")
        print(f"  Rendering center set to L5: {l5_center}")

        # 显示生成的文件
        print("  Generated files:")
        for file in os.listdir(output_dir):
            file_path = os.path.join(output_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"    - {file} ({file_size:,} bytes)")

        if open_browser:
            print("\n  Starting local server...")
            start_local_server(output_dir)

    except Exception as e:
        print(f"✗ Failed to create server visualization: {e}")

def start_local_server(directory):
    """启动本地服务器"""
    try:
        import subprocess
        import time
        
        # 启动服务器（非阻塞）
        server_script = "serve_threejs.py"
        if os.path.exists(server_script):
            print(f"  Launching server for {directory}...")
            subprocess.Popen([
                sys.executable, server_script, 
                "-d", directory, 
                "-p", "8001"  # 使用不同端口避免冲突
            ])
            time.sleep(2)  # 给服务器时间启动
            print("  ✓ Server should be starting at http://localhost:8001")
        else:
            print(f"  Server script not found: {server_script}")
            print(f"  Please run: python serve_threejs.py -d {directory}")
            
    except Exception as e:
        print(f"  Could not start server automatically: {e}")
        print(f"  Please run manually: python serve_threejs.py -d {directory}")

def update_server_html_with_l5_center(output_dir, l5_center):
    """更新服务器版本的HTML文件以使用L5中心"""
    try:
        html_file = os.path.join(output_dir, "index.html")
        if not os.path.exists(html_file):
            print(f"Warning: HTML file not found: {html_file}")
            return

        # 读取HTML文件
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 查找并替换渲染中心配置
        import re

        # 查找现有的RENDERING_CENTER配置
        pattern = r'const RENDERING_CENTER = \[.*?\];'
        replacement = f'const RENDERING_CENTER = [{l5_center[0]:.8f}, {l5_center[1]:.8f}, {l5_center[2]:.8f}];'

        if re.search(pattern, html_content):
            html_content = re.sub(pattern, replacement, html_content)
        else:
            # 如果没有找到，在viewer初始化前添加
            viewer_init_pattern = r'(const viewer = new SpineViewer\(\);)'
            if re.search(viewer_init_pattern, html_content):
                html_content = re.sub(
                    viewer_init_pattern,
                    f'{replacement}\n            \\1\n            viewer.setRenderingCenter(RENDERING_CENTER);',
                    html_content
                )

        # 写回文件
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"  Updated HTML with L5 center: {l5_center}")

    except Exception as e:
        print(f"Warning: Could not update HTML with L5 center: {e}")

def create_embedded_html(output_file, mesh_data, plans, l5_center):
    """创建嵌入数据的HTML文件（简化版本）"""
    import json
    
    # 将数据转换为JavaScript格式
    mesh_data_js = json.dumps(mesh_data, indent=2)
    plans_js = json.dumps(plans, indent=2)
    l5_center_js = json.dumps(l5_center)
    
    # 这里使用简化的HTML模板
    html_content = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spine Puncture Planning - Embedded Visualization</title>
    <style>
        body {{ margin: 0; padding: 0; background: #1a1a1a; color: white; font-family: Arial, sans-serif; overflow: hidden; }}
        #container {{ position: relative; width: 100vw; height: 100vh; }}
        #controls {{ position: absolute; top: 20px; left: 20px; background: rgba(0, 0, 0, 0.8); padding: 20px; border-radius: 10px; min-width: 250px; z-index: 1000; }}
        #controls h3 {{ margin-top: 0; color: #4ecdc4; }}
        .control-group {{ margin-bottom: 15px; }}
        .control-group label {{ display: block; margin-bottom: 5px; font-weight: bold; }}
        .control-group input[type="checkbox"] {{ margin-right: 8px; }}
        .control-group input[type="range"] {{ width: 100%; }}
        .control-group select, .control-group button {{ width: 100%; padding: 8px; border: none; border-radius: 4px; background: #333; color: white; }}
        .control-group button {{ background: #4ecdc4; cursor: pointer; margin-top: 5px; }}
        .control-group button:hover {{ background: #45b7d1; }}
        #info {{ position: absolute; bottom: 20px; left: 20px; background: rgba(0, 0, 0, 0.8); padding: 15px; border-radius: 10px; max-width: 300px; }}
        #loading {{ position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.9); padding: 20px; border-radius: 10px; text-align: center; z-index: 2000; }}
        .hidden {{ display: none !important; }}
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <h3>Loading 3D Visualization...</h3>
            <p>Please wait while the spine model is being rendered.</p>
        </div>
        
        <div id="controls">
            <h3>Spine Puncture Planning</h3>
            <div class="control-group">
                <label>Show Vertebrae:</label>
                <div>
                    <input type="checkbox" id="showL3" checked> L3
                    <input type="checkbox" id="showL4" checked> L4
                    <input type="checkbox" id="showL5" checked> L5
                    <input type="checkbox" id="showS1" checked> S1
                </div>
            </div>
            <div class="control-group">
                <label for="punctureLevel">Puncture Planning:</label>
                <select id="punctureLevel">
                    <option value="">Select Level</option>'''
    
    for plan_level in plans.keys():
        html_content += f'<option value="{plan_level}">{plan_level}</option>'
    
    html_content += f'''
                </select>
            </div>
            <div class="control-group">
                <label for="opacity">Vertebra Opacity:</label>
                <input type="range" id="opacity" min="0.1" max="1.0" step="0.1" value="0.7">
                <span id="opacityValue">0.7</span>
            </div>
            <div class="control-group">
                <button id="resetView">Reset View</button>
            </div>
        </div>
        
        <div id="info">
            <h4>3D Spine Puncture Planning</h4>
            <p>Embedded visualization - Use mouse to rotate, scroll to zoom</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    
    <script>
        const MESH_DATA = {mesh_data_js};
        const PLAN_DATA = {plans_js};
        const L5_CENTER = {l5_center_js};

        // 简化的SpineViewer类（省略详细实现以节省空间）
        class SpineViewer {{
            constructor() {{
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x1a1a1a);
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);

                // 设置相机位置相对于L5中心
                this.camera.position.set(L5_CENTER[0], L5_CENTER[1], L5_CENTER[2] + 200);
                this.camera.lookAt(new THREE.Vector3(...L5_CENTER));

                this.renderer = new THREE.WebGLRenderer({{ antialias: true }});
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                document.getElementById('container').appendChild(this.renderer.domElement);

                this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
                this.controls.target.set(...L5_CENTER);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;

                this.vertebraMeshes = {{}};
                this.colors = {{ L3: 0xff6b6b, L4: 0x4ecdc4, L5: 0x45b7d1, S1: 0xf9ca24 }};
                this.setupLighting();
                this.animate();
                this.loadEmbeddedData();
                this.setupEventListeners();
            }}
            
            setupLighting() {{
                this.scene.add(new THREE.AmbientLight(0x404040, 0.6));
                const light = new THREE.DirectionalLight(0xffffff, 0.8);
                light.position.set(100, 100, 50);
                this.scene.add(light);
            }}
            
            animate() {{
                requestAnimationFrame(() => this.animate());
                this.controls.update();
                this.renderer.render(this.scene, this.camera);
            }}
            
            loadEmbeddedData() {{
                this.createVertebraeFromMeshData(MESH_DATA);
                this.puncturePoints = PLAN_DATA;
                document.getElementById('loading').classList.add('hidden');
            }}
            
            createVertebraeFromMeshData(meshData) {{
                Object.keys(meshData).forEach(label => {{
                    if (this.colors[label] && meshData[label].vertices) {{
                        const data = meshData[label];
                        const geometry = new THREE.BufferGeometry();
                        geometry.setAttribute('position', new THREE.Float32BufferAttribute(data.vertices, 3));
                        if (data.faces) geometry.setIndex(data.faces);
                        geometry.computeVertexNormals();
                        const material = new THREE.MeshLambertMaterial({{ color: this.colors[label], transparent: true, opacity: 0.7 }});
                        const mesh = new THREE.Mesh(geometry, material);
                        this.vertebraMeshes[label] = mesh;
                        this.scene.add(mesh);
                    }}
                }});
            }}
            
            setupEventListeners() {{
                ['L3', 'L4', 'L5', 'S1'].forEach(v => {{
                    const cb = document.getElementById(`show${{v}}`);
                    if (cb) cb.addEventListener('change', (e) => {{
                        if (this.vertebraMeshes[v]) this.vertebraMeshes[v].visible = e.target.checked;
                    }});
                }});
                
                const planSelect = document.getElementById('punctureLevel');
                if (planSelect) planSelect.addEventListener('change', (e) => this.showPuncturePlan(e.target.value));
                
                const opacitySlider = document.getElementById('opacity');
                if (opacitySlider) opacitySlider.addEventListener('input', (e) => {{
                    const value = parseFloat(e.target.value);
                    document.getElementById('opacityValue').textContent = value.toFixed(1);
                    Object.values(this.vertebraMeshes).forEach(mesh => mesh.material.opacity = value);
                }});
                
                const resetBtn = document.getElementById('resetView');
                if (resetBtn) resetBtn.addEventListener('click', () => {{
                    this.camera.position.set(L5_CENTER[0], L5_CENTER[1], L5_CENTER[2] + 200);
                    this.controls.target.set(...L5_CENTER);
                    this.camera.lookAt(new THREE.Vector3(...L5_CENTER));
                    this.controls.update();
                }});
                
                window.addEventListener('resize', () => {{
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                }});
            }}
            
            showPuncturePlan(level) {{
                this.clearPuncturePoints();
                if (!level || !this.puncturePoints[level]) return;
                
                const plan = this.puncturePoints[level];
                if (plan.target_point_A) {{
                    const targetMesh = new THREE.Mesh(
                        new THREE.SphereGeometry(2, 16, 16),
                        new THREE.MeshLambertMaterial({{ color: 0xff0000 }})
                    );
                    targetMesh.position.set(...plan.target_point_A);
                    targetMesh.name = 'target_point_A';
                    this.scene.add(targetMesh);
                }}
                
                ['upper_vertebra', 'lower_vertebra'].forEach(vertebra => {{
                    const vData = plan[vertebra];
                    if (vData) {{
                        ['left_articular_tip_B', 'right_articular_tip_B'].forEach(tip => {{
                            if (vData[tip]) {{
                                const bMesh = new THREE.Mesh(
                                    new THREE.SphereGeometry(1.5, 12, 12),
                                    new THREE.MeshLambertMaterial({{ color: 0x00ff00 }})
                                );
                                bMesh.position.set(...vData[tip]);
                                bMesh.name = `${{vertebra}}_${{tip}}`;
                                this.scene.add(bMesh);
                            }}
                        }});
                    }}
                }});
            }}
            
            clearPuncturePoints() {{
                const toRemove = [];
                this.scene.traverse(obj => {{
                    if (obj.name && (obj.name.includes('target_point') || obj.name.includes('articular_tip'))) {{
                        toRemove.push(obj);
                    }}
                }});
                toRemove.forEach(obj => this.scene.remove(obj));
            }}
        }}
        
        window.addEventListener('load', () => new SpineViewer());
    </script>
</body>
</html>'''
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

if __name__ == "__main__":
    main()
