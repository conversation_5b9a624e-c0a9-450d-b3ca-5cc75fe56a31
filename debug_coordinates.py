#!/usr/bin/env python3
"""
调试坐标系统的脚本
"""

import os
import json
import numpy as np
from puncture_planning import PuncturePlanner

def debug_coordinates():
    """调试坐标系统"""
    
    # 检查分割文件
    segmentation_file = 'spine_pelvis_output_final.nii.gz'
    if not os.path.exists(segmentation_file):
        print(f"Error: Segmentation file not found: {segmentation_file}")
        return
    
    print("=" * 80)
    print("Debugging Coordinate Systems")
    print("=" * 80)
    
    # 创建穿刺规划器
    planner = PuncturePlanner(segmentation_file)
    
    # 1. 检查L5椎骨网格的中心
    print("\n1. Analyzing L5 vertebra mesh...")
    l5_vertices, l5_faces = planner.generate_vertebra_surface(24, simplify_factor=10)
    
    if l5_vertices is not None:
        print(f"   L5 mesh vertices shape: {l5_vertices.shape}")
        print(f"   L5 mesh center (world): {np.mean(l5_vertices, axis=0)}")
        
        # 应用缩放
        l5_vertices_scaled = l5_vertices / 10.0
        l5_mesh_center_scaled = np.mean(l5_vertices_scaled, axis=0)
        print(f"   L5 mesh center (scaled): {l5_mesh_center_scaled}")
        
        # 坐标范围
        print(f"   L5 mesh X range (scaled): {np.min(l5_vertices_scaled[:, 0]):.2f} to {np.max(l5_vertices_scaled[:, 0]):.2f}")
        print(f"   L5 mesh Y range (scaled): {np.min(l5_vertices_scaled[:, 1]):.2f} to {np.max(l5_vertices_scaled[:, 1]):.2f}")
        print(f"   L5 mesh Z range (scaled): {np.min(l5_vertices_scaled[:, 2]):.2f} to {np.max(l5_vertices_scaled[:, 2]):.2f}")
    
    # 2. 检查L5质心计算
    print("\n2. Analyzing L5 centroid calculation...")
    l5_mask = planner.extract_vertebra_mask(24)
    l5_centroid_voxel = planner.find_vertebra_centroid(l5_mask)
    
    print(f"   L5 centroid (voxel): {l5_centroid_voxel}")
    
    # 转换为世界坐标
    homogeneous = np.append(l5_centroid_voxel, 1)
    l5_center_world = planner.affine @ homogeneous
    l5_center_world = l5_center_world[:3]
    l5_center_scaled = l5_center_world / 10.0
    
    print(f"   L5 centroid (world): {l5_center_world}")
    print(f"   L5 centroid (scaled): {l5_center_scaled}")
    
    # 3. 比较两种方法的差异
    if l5_vertices is not None:
        print("\n3. Comparing mesh center vs centroid...")
        difference = l5_mesh_center_scaled - l5_center_scaled
        print(f"   Difference (mesh center - centroid): {difference}")
        print(f"   Distance: {np.linalg.norm(difference):.2f}")
    
    # 4. 检查穿刺点位置
    print("\n4. Analyzing puncture points...")
    
    # 规划L4/L5穿刺
    l4l5_plan = planner.plan_puncture_path('L4L5')
    if l4l5_plan:
        target_point = l4l5_plan['target_point_A']
        target_point_scaled = [c / 10.0 for c in target_point]
        
        print(f"   L4/L5 target point (world): {target_point}")
        print(f"   L4/L5 target point (scaled): {target_point_scaled}")
        
        # 与L5中心的距离
        distance_to_l5 = np.linalg.norm(np.array(target_point_scaled) - l5_center_scaled)
        print(f"   Distance to L5 centroid: {distance_to_l5:.2f}")
        
        # 检查是否在L5网格范围内
        if l5_vertices is not None:
            target_array = np.array(target_point_scaled)
            in_x_range = (np.min(l5_vertices_scaled[:, 0]) <= target_array[0] <= np.max(l5_vertices_scaled[:, 0]))
            in_y_range = (np.min(l5_vertices_scaled[:, 1]) <= target_array[1] <= np.max(l5_vertices_scaled[:, 1]))
            in_z_range = (np.min(l5_vertices_scaled[:, 2]) <= target_array[2] <= np.max(l5_vertices_scaled[:, 2]))
            
            print(f"   Target point in L5 X range: {in_x_range}")
            print(f"   Target point in L5 Y range: {in_y_range}")
            print(f"   Target point in L5 Z range: {in_z_range}")
    
    # 5. 检查仿射矩阵
    print("\n5. Analyzing affine transformation...")
    print(f"   Affine matrix:")
    print(f"   {planner.affine}")
    
    # 检查体素间距
    voxel_spacing = np.abs(np.diag(planner.affine)[:3])
    print(f"   Voxel spacing: {voxel_spacing}")
    
    # 6. 建议的修正
    print("\n6. Recommendations...")
    
    if l5_vertices is not None:
        # 使用网格中心作为渲染中心可能更准确
        print(f"   Consider using mesh center as rendering center: {l5_mesh_center_scaled}")
        
        # 检查是否需要调整坐标系
        if l4l5_plan:
            target_array = np.array(target_point_scaled)
            if not (np.min(l5_vertices_scaled[:, 2]) <= target_array[2] <= np.max(l5_vertices_scaled[:, 2])):
                print("   WARNING: Target point Z coordinate is outside L5 mesh range!")
                print("   This suggests a coordinate system mismatch.")
    
    return l5_mesh_center_scaled if l5_vertices is not None else l5_center_scaled

if __name__ == "__main__":
    debug_coordinates()
