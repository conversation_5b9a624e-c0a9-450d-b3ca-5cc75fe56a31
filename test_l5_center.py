#!/usr/bin/env python3
"""
测试L5中心计算的脚本
"""

import os
import sys
from puncture_planning import PuncturePlanner
import numpy as np

def test_l5_center():
    """测试L5中心计算"""
    
    # 检查分割文件
    segmentation_file = 'spine_pelvis_output_final.nii.gz'
    if not os.path.exists(segmentation_file):
        print(f"Error: Segmentation file not found: {segmentation_file}")
        return
    
    print("=" * 60)
    print("Testing L5 Center Calculation")
    print("=" * 60)
    
    # 创建穿刺规划器
    planner = PuncturePlanner(segmentation_file)
    
    # 提取L5椎体mask (标签24)
    print("1. Extracting L5 vertebra mask...")
    l5_mask = planner.extract_vertebra_mask(24)
    
    if not l5_mask.any():
        print("Error: L5 vertebra not found")
        return
    
    print(f"   L5 mask shape: {l5_mask.shape}")
    print(f"   L5 voxel count: {np.sum(l5_mask)}")
    
    # 计算L5质心（体素坐标）
    print("\n2. Calculating L5 centroid in voxel coordinates...")
    l5_centroid_voxel = planner.find_vertebra_centroid(l5_mask)
    
    if l5_centroid_voxel is None:
        print("Error: Could not calculate L5 centroid")
        return
    
    print(f"   L5 centroid (voxel): {l5_centroid_voxel}")
    
    # 转换为世界坐标
    print("\n3. Converting to world coordinates...")
    homogeneous = np.append(l5_centroid_voxel, 1)
    l5_center_world = planner.affine @ homogeneous
    l5_center_world = l5_center_world[:3]
    
    print(f"   L5 center (world): {l5_center_world}")
    print(f"   Affine matrix shape: {planner.affine.shape}")
    
    # 应用与网格数据相同的缩放因子
    print("\n4. Applying scaling for web visualization...")
    l5_center_scaled = l5_center_world / 10.0
    
    print(f"   L5 center (scaled): {l5_center_scaled}")
    
    # 比较与其他椎体的位置
    print("\n5. Comparing with other vertebrae...")
    for label, name in planner.vertebra_labels.items():
        if label == 24:  # L5
            continue
        
        mask = planner.extract_vertebra_mask(label)
        if mask.any():
            centroid_voxel = planner.find_vertebra_centroid(mask)
            if centroid_voxel is not None:
                homogeneous = np.append(centroid_voxel, 1)
                center_world = planner.affine @ homogeneous
                center_world = center_world[:3]
                center_scaled = center_world / 10.0
                
                distance = np.linalg.norm(l5_center_scaled - center_scaled)
                print(f"   {name} center (scaled): {center_scaled}")
                print(f"   Distance from L5: {distance:.2f}")
    
    print("\n6. Summary:")
    print(f"   L5 center for Three.js rendering: {l5_center_scaled.tolist()}")
    print("   This will be used as the camera target and orbit center.")
    
    return l5_center_scaled

if __name__ == "__main__":
    test_l5_center()
