#!/usr/bin/env python3
"""
验证穿刺点球体大小修复的脚本
"""

import re
import os

def verify_sphere_sizes():
    """验证所有文件中的球体大小是否正确"""
    
    print("=" * 80)
    print("Verifying Sphere Size Fixes")
    print("=" * 80)
    
    files_to_check = [
        ('spine_visualization_embedded.html', 'Embedded HTML'),
        ('threejs_output/spine_viewer.js', 'Server JavaScript'),
        ('threejs_template.js', 'JavaScript Template'),
        ('complete_threejs_demo.py', 'Python Demo Script'),
        ('puncture_planning.py', 'Python Planning Script')
    ]
    
    expected_sizes = {
        'target': 0.5,  # 目标点A（红色）
        'articular': 0.3  # 关节突点B（绿色）
    }
    
    all_correct = True
    
    for file_path, file_desc in files_to_check:
        print(f"\n{file_desc}: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"  ❌ File not found")
            all_correct = False
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找SphereGeometry模式
            sphere_patterns = re.findall(r'SphereGeometry\(([\d.]+)', content)
            
            if not sphere_patterns:
                print(f"  ⚠️  No SphereGeometry found")
                continue
            
            print(f"  Found {len(sphere_patterns)} sphere geometries:")
            
            file_correct = True
            for i, size_str in enumerate(sphere_patterns):
                size = float(size_str)
                print(f"    Sphere {i+1}: radius = {size}")
                
                # 检查大小是否合理（应该是0.3或0.5）
                if size in [0.3, 0.5]:
                    print(f"      ✅ Correct size")
                elif size in [1.5, 2.0]:
                    print(f"      ❌ Old size detected!")
                    file_correct = False
                    all_correct = False
                else:
                    print(f"      ⚠️  Unexpected size")
            
            if file_correct and sphere_patterns:
                print(f"  ✅ All spheres in this file have correct sizes")
            
        except Exception as e:
            print(f"  ❌ Error reading file: {e}")
            all_correct = False
    
    print("\n" + "=" * 80)
    print("Summary")
    print("=" * 80)
    
    if all_correct:
        print("🎉 ALL FILES HAVE CORRECT SPHERE SIZES!")
        print(f"  ✅ Target points (red): radius = {expected_sizes['target']}")
        print(f"  ✅ Articular points (green): radius = {expected_sizes['articular']}")
        print("\nThe visualization should now show:")
        print("  - Appropriately sized puncture points relative to vertebrae")
        print("  - Better visual balance between vertebrae and puncture markers")
        print("  - Clearer anatomical structure representation")
    else:
        print("❌ SOME FILES STILL HAVE INCORRECT SPHERE SIZES!")
        print("  Please check the files marked with errors above.")
    
    return all_correct

if __name__ == "__main__":
    verify_sphere_sizes()
