#!/usr/bin/env python3
"""
简单的Web服务器用于运行脊椎穿刺规划可视化
"""

import os
import http.server
import socketserver
import webbrowser
import argparse
from pathlib import Path

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def start_server(port=8000, directory=None, open_browser=True):
    """
    启动Web服务器
    
    Args:
        port: 端口号
        directory: 服务目录
        open_browser: 是否自动打开浏览器
    """
    if directory:
        os.chdir(directory)
    
    handler = CORSHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"服务器启动成功!")
            print(f"地址: http://localhost:{port}")
            print(f"服务目录: {os.getcwd()}")
            print("按 Ctrl+C 停止服务器")
            
            if open_browser:
                webbrowser.open(f'http://localhost:{port}')
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"错误: 端口 {port} 已被占用，请尝试其他端口")
        else:
            print(f"启动服务器时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description="启动脊椎穿刺规划Web可视化服务器")
    parser.add_argument("-p", "--port", type=int, default=8000, help="端口号 (默认: 8000)")
    parser.add_argument("-d", "--directory", help="服务目录 (默认: 当前目录)")
    parser.add_argument("--no-browser", action="store_true", help="不自动打开浏览器")
    
    args = parser.parse_args()
    
    # 如果没有指定目录，使用当前脚本所在目录
    if not args.directory:
        args.directory = Path(__file__).parent
    
    start_server(
        port=args.port,
        directory=args.directory,
        open_browser=not args.no_browser
    )

if __name__ == "__main__":
    main()
