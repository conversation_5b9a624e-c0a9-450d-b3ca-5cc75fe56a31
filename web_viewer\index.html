<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脊椎穿刺规划3D可视化</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            color: white;
            min-width: 300px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input, .control-group select {
            width: 100%;
            padding: 5px;
            border: none;
            border-radius: 3px;
            background: #333;
            color: white;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .control-group button:hover {
            background: #45a049;
        }
        
        .control-group button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            color: white;
            max-width: 400px;
        }
        
        .legend {
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 200;
            background: rgba(0, 0, 0, 0.9);
            padding: 30px;
            border-radius: 10px;
            color: white;
            text-align: center;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <div class="spinner"></div>
            <div>正在加载3D模型...</div>
        </div>
        
        <div id="controls">
            <h3>脊椎穿刺规划控制面板</h3>
            
            <div class="control-group">
                <label>分割文件:</label>
                <input type="file" id="segmentationFile" accept=".nii,.nii.gz" />
            </div>
            
            <div class="control-group">
                <label>穿刺规划文件:</label>
                <input type="file" id="planningFile" accept=".json" />
            </div>
            
            <div class="control-group">
                <button id="loadData">加载数据</button>
            </div>
            
            <div class="control-group">
                <label>显示椎体:</label>
                <div>
                    <input type="checkbox" id="showL3" checked> L3
                    <input type="checkbox" id="showL4" checked> L4
                    <input type="checkbox" id="showL5" checked> L5
                    <input type="checkbox" id="showS1" checked> S1
                </div>
            </div>
            
            <div class="control-group">
                <label>穿刺规划:</label>
                <select id="punctureLevel">
                    <option value="">选择椎间隙</option>
                    <option value="L4L5">L4/L5</option>
                    <option value="L5S1">L5/S1</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>透明度:</label>
                <input type="range" id="opacity" min="0.1" max="1" step="0.1" value="0.8" />
            </div>
            
            <div class="control-group">
                <button id="resetView">重置视角</button>
            </div>
        </div>
        
        <div id="info">
            <h4>脊椎穿刺规划可视化</h4>
            <p>使用鼠标拖拽旋转视角，滚轮缩放</p>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff6b6b;"></div>
                    <span>L3椎体</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ecdc4;"></div>
                    <span>L4椎体</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45b7d1;"></div>
                    <span>L5椎体</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f9ca24;"></div>
                    <span>S1椎体</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff0000;"></div>
                    <span>穿刺目标点A</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #00ff00;"></div>
                    <span>关节突尖部B</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Three.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.9/dat.gui.min.js"></script>
    
    <!-- 控制器 -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    
    <!-- NIfTI 文件读取库 -->
    <script src="https://cdn.jsdelivr.net/npm/nifti-reader-js@0.6.8/release/nifti-reader.min.js"></script>
    
    <!-- 主要脚本 -->
    <script src="spine_viewer.js"></script>
</body>
</html>
