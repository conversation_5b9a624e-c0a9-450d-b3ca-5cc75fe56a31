/**
 * 脊椎穿刺规划3D可视化器
 */

class SpineViewer {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        
        this.vertebraMeshes = {};
        this.puncturePoints = {};
        this.currentPlan = null;
        
        this.colors = {
            L3: 0xff6b6b,
            L4: 0x4ecdc4,
            L5: 0x45b7d1,
            S1: 0xf9ca24
        };
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a1a);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 0, 200);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        document.getElementById('container').appendChild(this.renderer.domElement);
        
        // 创建控制器
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        
        // 添加光源
        this.setupLighting();
        
        // 开始渲染循环
        this.animate();
        
        // 隐藏加载界面
        document.getElementById('loading').classList.add('hidden');
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // 主光源
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
        
        // 补充光源
        const light2 = new THREE.DirectionalLight(0xffffff, 0.4);
        light2.position.set(-100, -100, -50);
        this.scene.add(light2);
    }
    
    setupEventListeners() {
        // 文件加载
        document.getElementById('loadData').addEventListener('click', () => {
            this.loadData();
        });
        
        // 椎体显示控制
        ['L3', 'L4', 'L5', 'S1'].forEach(vertebra => {
            document.getElementById(`show${vertebra}`).addEventListener('change', (e) => {
                this.toggleVertebra(vertebra, e.target.checked);
            });
        });
        
        // 穿刺规划选择
        document.getElementById('punctureLevel').addEventListener('change', (e) => {
            this.showPuncturePlan(e.target.value);
        });
        
        // 透明度控制
        document.getElementById('opacity').addEventListener('input', (e) => {
            this.setOpacity(parseFloat(e.target.value));
        });
        
        // 重置视角
        document.getElementById('resetView').addEventListener('click', () => {
            this.resetView();
        });
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
    }
    
    async loadData() {
        const segFile = document.getElementById('segmentationFile').files[0];
        const planFile = document.getElementById('planningFile').files[0];
        
        if (!segFile) {
            alert('请选择分割文件');
            return;
        }
        
        document.getElementById('loading').classList.remove('hidden');
        
        try {
            // 加载分割数据
            await this.loadSegmentation(segFile);
            
            // 加载穿刺规划数据
            if (planFile) {
                await this.loadPuncturePlan(planFile);
            }
            
        } catch (error) {
            console.error('加载数据失败:', error);
            alert('加载数据失败: ' + error.message);
        } finally {
            document.getElementById('loading').classList.add('hidden');
        }
    }
    
    async loadSegmentation(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    // 检查文件类型
                    if (file.name.endsWith('.json')) {
                        // 如果是JSON文件，直接加载网格数据
                        const meshData = JSON.parse(new TextDecoder().decode(e.target.result));
                        this.createVertebraeFromMeshData(meshData);
                    } else {
                        // 如果是NIfTI文件，创建示例数据
                        this.createExampleVertebrae();
                    }
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('文件读取失败'));

            if (file.name.endsWith('.json')) {
                reader.readAsArrayBuffer(file);
            } else {
                reader.readAsArrayBuffer(file);
            }
        });
    }
    
    createVertebraeFromMeshData(meshData) {
        // 从网格数据创建椎体
        Object.keys(meshData).forEach(label => {
            if (this.colors[label]) {
                const data = meshData[label];

                // 创建几何体
                const geometry = new THREE.BufferGeometry();
                geometry.setAttribute('position', new THREE.Float32BufferAttribute(data.vertices, 3));
                if (data.faces) {
                    geometry.setIndex(data.faces);
                }
                geometry.computeVertexNormals();

                // 创建材质
                const material = new THREE.MeshLambertMaterial({
                    color: this.colors[label],
                    transparent: true,
                    opacity: 0.8,
                    side: THREE.DoubleSide
                });

                // 创建网格
                const mesh = new THREE.Mesh(geometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;

                this.vertebraMeshes[label] = mesh;
                this.scene.add(mesh);
            }
        });
    }

    createExampleVertebrae() {
        // 创建示例椎体几何体
        const vertebraLabels = ['L3', 'L4', 'L5', 'S1'];
        const positions = [
            [0, 60, 0],   // L3
            [0, 20, 0],   // L4
            [0, -20, 0],  // L5
            [0, -60, 0]   // S1
        ];

        vertebraLabels.forEach((label, index) => {
            const geometry = new THREE.BoxGeometry(40, 15, 30);
            const material = new THREE.MeshLambertMaterial({
                color: this.colors[label],
                transparent: true,
                opacity: 0.8
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(...positions[index]);
            mesh.castShadow = true;
            mesh.receiveShadow = true;

            this.vertebraMeshes[label] = mesh;
            this.scene.add(mesh);
        });
    }
    
    async loadPuncturePlan(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const planData = JSON.parse(e.target.result);
                    this.currentPlan = planData;
                    
                    // 更新穿刺规划选择器
                    const selector = document.getElementById('punctureLevel');
                    selector.innerHTML = '<option value="">选择椎间隙</option>';
                    
                    if (planData.L4L5) {
                        selector.innerHTML += '<option value="L4L5">L4/L5</option>';
                    }
                    if (planData.L5S1) {
                        selector.innerHTML += '<option value="L5S1">L5/S1</option>';
                    }
                    
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('规划文件读取失败'));
            reader.readAsText(file);
        });
    }
    
    toggleVertebra(vertebra, visible) {
        if (this.vertebraMeshes[vertebra]) {
            this.vertebraMeshes[vertebra].visible = visible;
        }
    }
    
    setOpacity(opacity) {
        Object.values(this.vertebraMeshes).forEach(mesh => {
            mesh.material.opacity = opacity;
        });
    }
    
    showPuncturePlan(level) {
        // 清除之前的穿刺点
        Object.values(this.puncturePoints).forEach(point => {
            this.scene.remove(point);
        });
        this.puncturePoints = {};
        
        if (!level || !this.currentPlan || !this.currentPlan[level]) {
            return;
        }
        
        const plan = this.currentPlan[level];
        
        // 显示目标点A
        if (plan.target_point_A) {
            const targetGeometry = new THREE.SphereGeometry(3, 16, 16);
            const targetMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
            const targetMesh = new THREE.Mesh(targetGeometry, targetMaterial);
            
            // 转换坐标（这里需要根据实际的坐标系进行调整）
            targetMesh.position.set(
                plan.target_point_A[0] / 10,
                plan.target_point_A[1] / 10,
                plan.target_point_A[2] / 10
            );
            
            this.puncturePoints.targetA = targetMesh;
            this.scene.add(targetMesh);
        }
        
        // 显示关节突尖部B点
        const bPointGeometry = new THREE.SphereGeometry(2, 12, 12);
        const bPointMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
        
        ['upper_vertebra', 'lower_vertebra'].forEach(vertebra => {
            if (plan[vertebra]) {
                ['left_articular_tip_B', 'right_articular_tip_B'].forEach(tip => {
                    if (plan[vertebra][tip]) {
                        const bMesh = new THREE.Mesh(bPointGeometry, bPointMaterial);
                        bMesh.position.set(
                            plan[vertebra][tip][0] / 10,
                            plan[vertebra][tip][1] / 10,
                            plan[vertebra][tip][2] / 10
                        );
                        
                        const key = `${vertebra}_${tip}`;
                        this.puncturePoints[key] = bMesh;
                        this.scene.add(bMesh);
                    }
                });
            }
        });
    }
    
    resetView() {
        this.camera.position.set(0, 0, 200);
        this.controls.reset();
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new SpineViewer();
});
