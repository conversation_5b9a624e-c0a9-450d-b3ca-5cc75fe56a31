#!/usr/bin/env python3
"""
测试修改后的关节突尖部定位算法
"""

import numpy as np
import nibabel as nib
from puncture_planning import PuncturePlanner

def test_articular_tips():
    """测试关节突尖部定位"""
    
    # 加载分割数据
    segmentation_file = "spine_pelvis_output_fixed.nii.gz"
    
    try:
        # 创建穿刺规划器
        planner = PuncturePlanner(segmentation_file)
        
        # 生成L4-L5穿刺计划
        print("生成L4-L5穿刺计划...")
        l4l5_plan = planner.plan_puncture_path('L4L5')
        
        if l4l5_plan:
            print("\n=== L4-L5穿刺计划 ===")
            print(f"目标椎间盘: {l4l5_plan['target_level']}")
            
            # 显示目标点A
            target_a = l4l5_plan['target_point_A']
            if target_a is not None:
                print(f"目标点A (椎间盘中心): [{target_a[0]:.1f}, {target_a[1]:.1f}, {target_a[2]:.1f}]")
            
            # 显示上椎体关节突尖部
            upper_left = l4l5_plan['upper_vertebra']['left_articular_tip_B']
            upper_right = l4l5_plan['upper_vertebra']['right_articular_tip_B']
            
            print("\n上椎体(L4)关节突尖部:")
            if upper_left is not None:
                print(f"  左侧B点: [{upper_left[0]:.1f}, {upper_left[1]:.1f}, {upper_left[2]:.1f}]")
            if upper_right is not None:
                print(f"  右侧B点: [{upper_right[0]:.1f}, {upper_right[1]:.1f}, {upper_right[2]:.1f}]")
            
            # 显示下椎体关节突尖部
            lower_left = l4l5_plan['lower_vertebra']['left_articular_tip_B']
            lower_right = l4l5_plan['lower_vertebra']['right_articular_tip_B']
            
            print("\n下椎体(L5)关节突尖部:")
            if lower_left is not None:
                print(f"  左侧B点: [{lower_left[0]:.1f}, {lower_left[1]:.1f}, {lower_left[2]:.1f}]")
            if lower_right is not None:
                print(f"  右侧B点: [{lower_right[0]:.1f}, {lower_right[1]:.1f}, {lower_right[2]:.1f}]")
        
        # 生成L5-S1穿刺计划
        print("\n" + "="*50)
        print("生成L5-S1穿刺计划...")
        l5s1_plan = planner.plan_puncture_path('L5S1')
        
        if l5s1_plan:
            print("\n=== L5-S1穿刺计划 ===")
            print(f"目标椎间盘: {l5s1_plan['target_level']}")
            
            # 显示目标点A
            target_a = l5s1_plan['target_point_A']
            if target_a is not None:
                print(f"目标点A (椎间盘中心): [{target_a[0]:.1f}, {target_a[1]:.1f}, {target_a[2]:.1f}]")
            
            # 显示上椎体关节突尖部
            upper_left = l5s1_plan['upper_vertebra']['left_articular_tip_B']
            upper_right = l5s1_plan['upper_vertebra']['right_articular_tip_B']
            
            print("\n上椎体(L5)关节突尖部:")
            if upper_left is not None:
                print(f"  左侧B点: [{upper_left[0]:.1f}, {upper_left[1]:.1f}, {upper_left[2]:.1f}]")
            if upper_right is not None:
                print(f"  右侧B点: [{upper_right[0]:.1f}, {upper_right[1]:.1f}, {upper_right[2]:.1f}]")
            
            # 显示下椎体关节突尖部
            lower_left = l5s1_plan['lower_vertebra']['left_articular_tip_B']
            lower_right = l5s1_plan['lower_vertebra']['right_articular_tip_B']
            
            print("\n下椎体(S1)关节突尖部:")
            if lower_left is not None:
                print(f"  左侧B点: [{lower_left[0]:.1f}, {lower_left[1]:.1f}, {lower_left[2]:.1f}]")
            if lower_right is not None:
                print(f"  右侧B点: [{lower_right[0]:.1f}, {lower_right[1]:.1f}, {lower_right[2]:.1f}]")
        
        # 生成可视化
        print("\n" + "="*50)
        print("生成Three.js可视化...")
        
        puncture_plans = []
        if l4l5_plan:
            puncture_plans.append(l4l5_plan)
        if l5s1_plan:
            puncture_plans.append(l5s1_plan)
            
        if puncture_plans:
            planner.generate_threejs_webpage(puncture_plans, output_dir="updated_articular_tips", auto_open=False)
            print("Three.js可视化已生成到 'updated_articular_tips' 目录")
            print("请打开 updated_articular_tips/index.html 查看结果")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_articular_tips()
