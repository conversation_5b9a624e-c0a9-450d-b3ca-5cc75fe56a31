/**
 * <PERSON>e Puncture Planning 3D Viewer
 * Generated automatically by Puncture<PERSON>lanner
 */

class SpineViewer {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;

        this.vertebraMeshes = {};
        this.puncturePoints = {};
        this.currentPlan = null;

        this.colors = {
            L3: 0xff6b6b,
            L4: 0x4ecdc4,
            L5: 0x45b7d1,
            S1: 0xf9ca24
        };

        this.init();
        this.setupEventListeners();
    }

    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a1a);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 0, 200);

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        document.getElementById('container').appendChild(this.renderer.domElement);

        // Create controls
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;

        // Add lighting
        this.setupLighting();

        // Start render loop
        this.animate();

        // Hide loading screen
        document.getElementById('loading').classList.add('hidden');
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Additional lights for better visibility
        const light1 = new THREE.DirectionalLight(0xffffff, 0.3);
        light1.position.set(-100, -100, -50);
        this.scene.add(light1);

        const light2 = new THREE.DirectionalLight(0xffffff, 0.3);
        light2.position.set(0, 100, -100);
        this.scene.add(light2);
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
    }

    setupEventListeners() {
        // Vertebra visibility controls
        ['L3', 'L4', 'L5', 'S1'].forEach(vertebra => {
            const checkbox = document.getElementById(`show${vertebra}`);
            if (checkbox) {
                checkbox.addEventListener('change', (e) => {
                    this.toggleVertebra(vertebra, e.target.checked);
                });
            }
        });

        // Puncture planning selection
        const planSelect = document.getElementById('punctureLevel');
        if (planSelect) {
            planSelect.addEventListener('change', (e) => {
                this.showPuncturePlan(e.target.value);
            });
        }

        // Opacity control
        const opacitySlider = document.getElementById('opacity');
        const opacityValue = document.getElementById('opacityValue');
        if (opacitySlider && opacityValue) {
            opacitySlider.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                opacityValue.textContent = value.toFixed(1);
                this.setOpacity(value);
            });
        }

        // Reset view
        const resetButton = document.getElementById('resetView');
        if (resetButton) {
            resetButton.addEventListener('click', () => {
                this.resetView();
            });
        }

        // Window resize
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
    }    
    async loadDataFromFiles(meshFile, planFile) {
        try {
            document.getElementById('loading').classList.remove('hidden');
            
            // Load mesh data
            const meshResponse = await fetch(meshFile);
            const meshData = await meshResponse.json();
            this.createVertebraeFromMeshData(meshData);
            
            // Load plan data
            const planResponse = await fetch(planFile);
            const planData = await planResponse.json();
            this.puncturePoints = planData;
            
            console.log('Data loaded successfully');
            
        } catch (error) {
            console.error('Failed to load data:', error);
            alert('Failed to load visualization data: ' + error.message);
        } finally {
            document.getElementById('loading').classList.add('hidden');
        }
    }
    
    createVertebraeFromMeshData(meshData) {
        Object.keys(meshData).forEach(label => {
            if (this.colors[label]) {
                const data = meshData[label];

                // Create geometry
                const geometry = new THREE.BufferGeometry();
                geometry.setAttribute('position', new THREE.Float32BufferAttribute(data.vertices, 3));
                if (data.faces) {
                    geometry.setIndex(data.faces);
                }
                geometry.computeVertexNormals();

                // Create material
                const material = new THREE.MeshLambertMaterial({
                    color: this.colors[label],
                    transparent: true,
                    opacity: 0.7,
                    side: THREE.DoubleSide
                });

                // Create mesh
                const mesh = new THREE.Mesh(geometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;

                this.vertebraMeshes[label] = mesh;
                this.scene.add(mesh);
                
                console.log(`Created ${label} vertebra with ${data.vertex_count} vertices`);
            }
        });
    }
    
    toggleVertebra(vertebra, visible) {
        if (this.vertebraMeshes[vertebra]) {
            this.vertebraMeshes[vertebra].visible = visible;
        }
    }
    
    setOpacity(opacity) {
        Object.values(this.vertebraMeshes).forEach(mesh => {
            mesh.material.opacity = opacity;
        });
    }
    
    showPuncturePlan(level) {
        // Clear existing puncture points
        this.clearPuncturePoints();
        
        if (!level || !this.puncturePoints[level]) {
            this.currentPlan = null;
            return;
        }
        
        this.currentPlan = level;
        const plan = this.puncturePoints[level];
        
        // Create target point A
        if (plan.target_point_A) {
            const targetGeometry = new THREE.SphereGeometry(2, 16, 16);
            const targetMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
            const targetMesh = new THREE.Mesh(targetGeometry, targetMaterial);
            targetMesh.position.set(...plan.target_point_A);
            targetMesh.name = 'target_point_A';
            this.scene.add(targetMesh);
        }
        
        // Create B points
        const bPointGeometry = new THREE.SphereGeometry(1.5, 12, 12);
        const bPointMaterial = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
        
        ['upper_vertebra', 'lower_vertebra'].forEach(vertebra => {
            const vertebraData = plan[vertebra];
            if (vertebraData) {
                ['left_articular_tip_B', 'right_articular_tip_B'].forEach(tip => {
                    if (vertebraData[tip]) {
                        const bMesh = new THREE.Mesh(bPointGeometry, bPointMaterial);
                        bMesh.position.set(...vertebraData[tip]);
                        bMesh.name = `${vertebra}_${tip}`;
                        this.scene.add(bMesh);
                    }
                });
            }
        });
        
        // Create puncture path lines
        if (plan.target_point_A) {
            const lineMaterial = new THREE.LineBasicMaterial({ 
                color: 0xff0000, 
                transparent: true, 
                opacity: 0.6 
            });
            
            ['upper_vertebra', 'lower_vertebra'].forEach(vertebra => {
                const vertebraData = plan[vertebra];
                if (vertebraData) {
                    ['left_articular_tip_B', 'right_articular_tip_B'].forEach(tip => {
                        if (vertebraData[tip]) {
                            const points = [
                                new THREE.Vector3(...vertebraData[tip]),
                                new THREE.Vector3(...plan.target_point_A)
                            ];
                            const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
                            const line = new THREE.Line(lineGeometry, lineMaterial);
                            line.name = `path_${vertebra}_${tip}`;
                            this.scene.add(line);
                        }
                    });
                }
            });
        }
        
        console.log(`Showing puncture plan for ${level}`);
    }
    
    clearPuncturePoints() {
        const objectsToRemove = [];
        this.scene.traverse((object) => {
            if (object.name && (
                object.name.includes('target_point') ||
                object.name.includes('articular_tip') ||
                object.name.includes('path_')
            )) {
                objectsToRemove.push(object);
            }
        });
        
        objectsToRemove.forEach(object => {
            this.scene.remove(object);
        });
    }
    
    resetView() {
        this.camera.position.set(0, 0, 200);
        this.controls.reset();
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
}

// Export for use
window.SpineViewer = SpineViewer;
