# 椎骨可视化功能说明

## 概述

我们已经成功为 `puncture_planning.py` 添加了椎骨3D可视化功能。现在在穿刺规划的可视化中可以显示椎骨的3D模型，这样可以更直观地验证穿刺点的准确性。

## 新增功能

### 1. 椎骨3D表面生成
- **方法**: `generate_vertebra_surface(label, simplify_factor=5)`
- **功能**: 使用marching cubes算法从分割数据生成椎骨的3D表面网格
- **参数**:
  - `label`: 椎体标签 (22=L3, 23=L4, 24=L5, 27=S1)
  - `simplify_factor`: 简化因子，用于减少网格复杂度，提高渲染性能

### 2. 增强的可视化方法
- **方法**: `visualize_puncture_plan(puncture_plan, show_vertebrae=True)`
- **功能**: 可视化穿刺规划，可选择是否显示椎骨3D模型
- **参数**:
  - `puncture_plan`: 穿刺规划数据
  - `show_vertebrae`: 是否显示椎骨3D模型 (默认True)

### 3. 简化可视化方法
- **方法**: `visualize_puncture_plan_simple(puncture_plan)`
- **功能**: 仅显示穿刺点，不显示椎骨模型的简化版本

## 可视化元素

### 颜色编码
- **红色球**: 穿刺目标点 A
- **深蓝色三角**: 上椎体关节突尖部 B 点
- **深绿色三角**: 下椎体关节突尖部 B 点
- **半透明表面**: 椎骨3D模型
  - L4椎体: 青色 (cyan)
  - L5椎体: 蓝色 (blue)
  - S1椎体: 黄色 (yellow)
- **红色虚线**: 从B点到A点的穿刺路径示意

### 交互功能
- **鼠标拖拽**: 旋转3D视角
- **滚轮**: 缩放
- **图例**: 显示各元素的含义

## 使用方法

### 基本使用
```python
from puncture_planning import PuncturePlanner

# 创建规划器
planner = PuncturePlanner('spine_pelvis_output_final.nii.gz')

# 规划穿刺路径
plan = planner.plan_puncture_path('L4L5')

# 显示带椎骨的完整可视化
planner.visualize_puncture_plan(plan, show_vertebrae=True)

# 或显示简化版本（仅穿刺点）
planner.visualize_puncture_plan_simple(plan)
```

### 运行演示
```bash
# 完整演示
python demo_vertebra_visualization.py

# 单个椎体测试
python demo_vertebra_visualization.py --individual

# 运行主程序
python puncture_planning.py
```

## 技术实现

### 3D表面生成
1. **数据提取**: 从NIfTI分割文件中提取指定椎体的mask
2. **表面重建**: 使用scikit-image的marching cubes算法生成3D表面
3. **坐标转换**: 将体素坐标转换为世界坐标系
4. **网格简化**: 通过降采样减少顶点数量，提高渲染性能

### 可视化渲染
1. **表面绘制**: 使用matplotlib的`plot_trisurf`绘制椎骨表面
2. **点标记**: 使用不同颜色和形状标记穿刺点
3. **路径线**: 绘制从B点到A点的穿刺路径示意线
4. **透明度**: 设置椎骨表面为半透明，便于观察内部结构

## 性能优化

### 网格简化
- 默认简化因子为3，在保持形状特征的同时减少计算量
- L4椎体: ~12,000顶点 → ~4,000顶点
- L5椎体: ~12,000顶点 → ~4,000顶点
- S1椎体: ~12,000顶点 → ~4,000顶点

### 渲染优化
- 使用半透明材质减少视觉复杂度
- 合理的视角设置提供最佳观察角度
- 图例和标签使用英文避免字体问题

## 故障排除

### 常见问题
1. **椎骨不显示**
   - 检查分割文件是否包含对应椎体标签
   - 确认椎体mask不为空

2. **渲染缓慢**
   - 增加`simplify_factor`参数值
   - 关闭椎骨显示使用简化模式

3. **中文字体警告**
   - 已改为英文标签，避免字体问题
   - 不影响功能，可以忽略

### 依赖要求
```bash
pip install nibabel numpy scipy matplotlib scikit-image
```

## 与Web可视化的关系

本功能是对现有Three.js Web可视化的补充：
- **matplotlib版本**: 适合快速验证和调试
- **Web版本**: 适合最终展示和交互
- **数据兼容**: 两者使用相同的分割数据和规划算法

## 下一步改进

1. **更多椎体**: 支持L3等其他椎体的显示
2. **材质优化**: 添加更真实的材质和光照效果
3. **动画功能**: 添加穿刺路径的动画演示
4. **导出功能**: 支持将可视化结果导出为图片或视频
