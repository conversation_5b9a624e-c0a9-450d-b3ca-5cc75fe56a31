import os
import json
import webbrowser
import nibabel as nib
import numpy as np
from scipy import ndimage
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from skimage import measure

class PuncturePlanner:
    def __init__(self, segmentation_path):
        """
        初始化穿刺路径规划器
        
        Args:
            segmentation_path: 脊椎骨盆分割结果文件路径
        """
        self.seg_img = nib.load(segmentation_path)
        self.seg_data = self.seg_img.get_fdata()
        self.affine = self.seg_img.affine
        
        # 椎体标签映射
        self.vertebra_labels = {
            22: 'L3',
            23: 'L4', 
            24: 'L5',
            27: 'S1'  # sacrum
        }
        
    def extract_vertebra_mask(self, label):
        """提取指定椎体的mask"""
        return self.seg_data == label
    
    def find_vertebra_centroid(self, mask):
        """计算椎体质心"""
        coords = np.where(mask)
        if len(coords[0]) == 0:
            return None
        centroid = np.array([np.mean(coords[i]) for i in range(3)])
        return centroid
    
    def find_endplate_center(self, vertebra_mask, is_upper=True):
        """
        找到椎体终板中心点(A点)
        
        Args:
            vertebra_mask: 椎体mask
            is_upper: True为上终板，False为下终板
        """
        coords = np.where(vertebra_mask)
        if len(coords[0]) == 0:
            return None
            
        # 获取椎体的z坐标范围
        z_coords = coords[0]
        
        if is_upper:
            # 上终板：取z坐标最大值附近的点
            target_z = np.max(z_coords)
            z_threshold = target_z - 2  # 允许2个体素的误差
        else:
            # 下终板：取z坐标最小值附近的点
            target_z = np.min(z_coords)
            z_threshold = target_z + 2
            
        # 筛选终板区域的点
        if is_upper:
            endplate_mask = (coords[0] >= z_threshold) & (coords[0] <= target_z)
        else:
            endplate_mask = (coords[0] >= target_z) & (coords[0] <= z_threshold)
            
        if np.sum(endplate_mask) == 0:
            return None
            
        # 计算终板中心
        endplate_coords = [coords[i][endplate_mask] for i in range(3)]
        center = np.array([np.mean(endplate_coords[i]) for i in range(3)])
        
        return center
    
    def find_articular_process_tips(self, vertebra_mask):
        """
        找到椎体两侧上关节突尖部(B点)
        
        Args:
            vertebra_mask: 椎体mask
            
        Returns:
            left_tip, right_tip: 左右两侧关节突尖部坐标
        """
        coords = np.where(vertebra_mask)
        if len(coords[0]) == 0:
            return None, None
            
        # 获取椎体的后部区域（关节突通常在后部）
        z_coords = coords[0]
        y_coords = coords[1] 
        x_coords = coords[2]
        
        # 取椎体后1/3部分
        y_min, y_max = np.min(y_coords), np.max(y_coords)
        posterior_threshold = y_min + (y_max - y_min) * 0.67
        posterior_mask = y_coords >= posterior_threshold
        
        if np.sum(posterior_mask) == 0:
            return None, None
            
        # 后部区域坐标
        post_coords = [coords[i][posterior_mask] for i in range(3)]
        
        # 分离左右两侧
        x_center = np.mean(post_coords[2])
        left_mask = post_coords[2] < x_center
        right_mask = post_coords[2] >= x_center
        
        # 找到左右两侧的最外侧点作为关节突尖部
        left_tip = None
        right_tip = None
        
        if np.sum(left_mask) > 0:
            left_coords = [post_coords[i][left_mask] for i in range(3)]
            # 左侧最外侧点
            min_x_idx = np.argmin(left_coords[2])
            left_tip = np.array([left_coords[i][min_x_idx] for i in range(3)])
            
        if np.sum(right_mask) > 0:
            right_coords = [post_coords[i][right_mask] for i in range(3)]
            # 右侧最外侧点
            max_x_idx = np.argmax(right_coords[2])
            right_tip = np.array([right_coords[i][max_x_idx] for i in range(3)])
            
        return left_tip, right_tip

    def generate_vertebra_surface(self, label, simplify_factor=5):
        """
        生成椎体的3D表面网格

        Args:
            label: 椎体标签
            simplify_factor: 简化因子，用于减少网格复杂度

        Returns:
            vertices, faces: 椎体表面的顶点和面
        """
        vertebra_mask = self.extract_vertebra_mask(label)

        if not np.any(vertebra_mask):
            return None, None

        try:
            # 使用marching cubes算法生成3D表面
            vertices, faces, normals, values = measure.marching_cubes(
                vertebra_mask.astype(np.float32),
                level=0.5,
                spacing=np.abs(np.diag(self.affine)[:3])
            )

            # 应用仿射变换将体素坐标转换为世界坐标
            vertices_homogeneous = np.column_stack([vertices, np.ones(len(vertices))])
            vertices_world = (self.affine @ vertices_homogeneous.T).T[:, :3]

            # 简化网格以提高渲染性能
            if simplify_factor > 1:
                # 简单的降采样方法
                step = simplify_factor
                simplified_vertices = vertices_world[::step]
                # 重新索引面
                vertex_map = {old_idx * step: new_idx for new_idx, old_idx in enumerate(range(0, len(vertices_world), step))}
                simplified_faces = []

                for face in faces:
                    new_face = []
                    valid_face = True
                    for vertex_idx in face:
                        if vertex_idx in vertex_map:
                            new_face.append(vertex_map[vertex_idx])
                        else:
                            valid_face = False
                            break
                    if valid_face and len(new_face) == 3:
                        simplified_faces.append(new_face)

                if len(simplified_faces) > 0:
                    return simplified_vertices, np.array(simplified_faces)

            return vertices_world, faces

        except Exception as e:
            print(f"Warning: Could not generate surface for vertebra {label}: {e}")
            return None, None

    def plan_puncture_path(self, target_level='L4L5'):
        """
        规划穿刺路径
        
        Args:
            target_level: 目标椎间隙 ('L4L5' 或 'L5S1')
            
        Returns:
            puncture_plan: 包含A点和B点坐标的字典
        """
        if target_level == 'L4L5':
            upper_label, lower_label = 23, 24  # L4, L5
        elif target_level == 'L5S1':
            upper_label, lower_label = 24, 27  # L5, S1
        else:
            raise ValueError("target_level must be 'L4L5' or 'L5S1'")
            
        # 提取椎体mask
        upper_mask = self.extract_vertebra_mask(upper_label)
        lower_mask = self.extract_vertebra_mask(lower_label)
        
        if not np.any(upper_mask) or not np.any(lower_mask):
            print(f"Warning: Could not find vertebrae for {target_level}")
            return None
            
        # 找到A点：椎体终板中心
        upper_endplate = self.find_endplate_center(upper_mask, is_upper=False)  # L4/L5下终板
        lower_endplate = self.find_endplate_center(lower_mask, is_upper=True)   # L5/S1上终板
        
        # 椎间隙中心作为穿刺目标点
        if upper_endplate is not None and lower_endplate is not None:
            target_point = (upper_endplate + lower_endplate) / 2
        else:
            print("Warning: Could not find endplate centers")
            return None
            
        # 找到B点：关节突尖部
        upper_left_tip, upper_right_tip = self.find_articular_process_tips(upper_mask)
        lower_left_tip, lower_right_tip = self.find_articular_process_tips(lower_mask)
        
        # 转换为物理坐标
        def voxel_to_world(coord):
            if coord is None:
                return None
            homogeneous = np.append(coord, 1)
            world_coord = self.affine @ homogeneous
            return world_coord[:3]
        
        puncture_plan = {
            'target_level': target_level,
            'target_point_A': voxel_to_world(target_point),
            'upper_vertebra': {
                'endplate_center': voxel_to_world(upper_endplate),
                'left_articular_tip_B': voxel_to_world(upper_left_tip),
                'right_articular_tip_B': voxel_to_world(upper_right_tip)
            },
            'lower_vertebra': {
                'endplate_center': voxel_to_world(lower_endplate),
                'left_articular_tip_B': voxel_to_world(lower_left_tip),
                'right_articular_tip_B': voxel_to_world(lower_right_tip)
            }
        }
        
        return puncture_plan
    
    def visualize_puncture_plan(self, puncture_plan, show_vertebrae=True):
        """
        可视化穿刺路径规划

        Args:
            puncture_plan: 穿刺规划数据
            show_vertebrae: 是否显示椎骨3D模型
        """
        if puncture_plan is None:
            print("No puncture plan to visualize")
            return

        fig = plt.figure(figsize=(15, 10))
        ax = fig.add_subplot(111, projection='3d')

        # 确定涉及的椎体标签
        target_level = puncture_plan['target_level']
        if target_level == 'L4L5':
            vertebra_labels = [23, 24]  # L4, L5
            vertebra_names = ['L4', 'L5']
            vertebra_colors = ['cyan', 'blue']
        elif target_level == 'L5S1':
            vertebra_labels = [24, 27]  # L5, S1
            vertebra_names = ['L5', 'S1']
            vertebra_colors = ['blue', 'yellow']
        else:
            vertebra_labels = []
            vertebra_names = []
            vertebra_colors = []

        # 显示椎骨3D模型
        if show_vertebrae and vertebra_labels:
            print("Generating vertebra 3D surfaces...")
            for i, (label, name, color) in enumerate(zip(vertebra_labels, vertebra_names, vertebra_colors)):
                vertices, faces = self.generate_vertebra_surface(label, simplify_factor=3)

                if vertices is not None and faces is not None:
                    # 绘制椎骨表面
                    ax.plot_trisurf(
                        vertices[:, 0], vertices[:, 1], vertices[:, 2],
                        triangles=faces,
                        alpha=0.3,
                        color=color,
                        label=f'{name} Vertebra'
                    )
                    print(f"Displayed {name} vertebra (vertices: {len(vertices)}, faces: {len(faces)})")
                else:
                    print(f"Warning: Could not generate {name} vertebra surface")

        # 绘制目标点A
        target = puncture_plan['target_point_A']
        if target is not None:
            ax.scatter(*target, c='red', s=150, label='Target Point A', marker='o', edgecolors='darkred', linewidth=2)

        # 绘制关节突尖部B点
        point_colors = ['darkblue', 'darkgreen']
        vertebrae = ['upper_vertebra', 'lower_vertebra']
        vertebra_labels_text = ['Upper Vertebra', 'Lower Vertebra']

        for i, (vert, vert_label) in enumerate(zip(vertebrae, vertebra_labels_text)):
            left_tip = puncture_plan[vert]['left_articular_tip_B']
            right_tip = puncture_plan[vert]['right_articular_tip_B']

            if left_tip is not None:
                ax.scatter(*left_tip, c=point_colors[i], s=100,
                          label=f'{vert_label} Left B Point', marker='^', edgecolors='black', linewidth=1)
            if right_tip is not None:
                ax.scatter(*right_tip, c=point_colors[i], s=100,
                          label=f'{vert_label} Right B Point', marker='^', edgecolors='black', linewidth=1)

        # 绘制穿刺路径线（从B点到A点的示意）
        if target is not None:
            for i, vert in enumerate(vertebrae):
                left_tip = puncture_plan[vert]['left_articular_tip_B']
                right_tip = puncture_plan[vert]['right_articular_tip_B']

                if left_tip is not None:
                    ax.plot([left_tip[0], target[0]], [left_tip[1], target[1]], [left_tip[2], target[2]],
                           'r--', alpha=0.6, linewidth=1)
                if right_tip is not None:
                    ax.plot([right_tip[0], target[0]], [right_tip[1], target[1]], [right_tip[2], target[2]],
                           'r--', alpha=0.6, linewidth=1)

        # 设置坐标轴
        ax.set_xlabel('X (mm)', fontsize=12)
        ax.set_ylabel('Y (mm)', fontsize=12)
        ax.set_zlabel('Z (mm)', fontsize=12)

        # 设置图例
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

        # 设置标题
        title = f'Spine Puncture Planning - {target_level} Intervertebral Space'
        if show_vertebrae:
            title += '\n(with 3D Vertebra Models)'
        ax.set_title(title, fontsize=14, fontweight='bold')

        # 设置视角
        ax.view_init(elev=20, azim=45)

        # 调整布局
        plt.tight_layout()
        plt.show()

    def visualize_puncture_plan_simple(self, puncture_plan):
        """
        简化的穿刺路径规划可视化（仅显示点，不显示椎骨）

        Args:
            puncture_plan: 穿刺规划数据
        """
        return self.visualize_puncture_plan(puncture_plan, show_vertebrae=False)

    def generate_web_mesh_data(self, simplify_factor=5):
        """
        生成Web可视化所需的网格数据

        Args:
            simplify_factor: 网格简化因子

        Returns:
            mesh_data: 包含所有椎体网格数据的字典
        """
        mesh_data = {}

        for label, name in self.vertebra_labels.items():
            print(f"Generating mesh for {name}...")
            vertices, faces = self.generate_vertebra_surface(label, simplify_factor)

            if vertices is not None and faces is not None:
                # 缩放到Web显示范围
                vertices_scaled = vertices / 10.0

                mesh_data[name] = {
                    'vertices': vertices_scaled.flatten().tolist(),
                    'faces': faces.flatten().tolist(),
                    'vertex_count': len(vertices_scaled),
                    'face_count': len(faces)
                }
                print(f"  {name}: {len(vertices_scaled)} vertices, {len(faces)} faces")
            else:
                print(f"  Warning: Could not generate mesh for {name}")

        return mesh_data

    def convert_plan_to_web_format(self, puncture_plan):
        """
        将穿刺规划数据转换为Web格式

        Args:
            puncture_plan: 穿刺规划数据

        Returns:
            web_plan: Web格式的穿刺规划数据
        """
        if puncture_plan is None:
            return None

        # 缩放坐标到Web显示范围
        def scale_coordinate(coord):
            if coord is None:
                return None
            return [c / 10.0 for c in coord]

        web_plan = {
            'target_level': puncture_plan['target_level'],
            'target_point_A': scale_coordinate(puncture_plan['target_point_A']),
            'upper_vertebra': {
                'endplate_center': scale_coordinate(puncture_plan['upper_vertebra']['endplate_center']),
                'left_articular_tip_B': scale_coordinate(puncture_plan['upper_vertebra']['left_articular_tip_B']),
                'right_articular_tip_B': scale_coordinate(puncture_plan['upper_vertebra']['right_articular_tip_B'])
            },
            'lower_vertebra': {
                'endplate_center': scale_coordinate(puncture_plan['lower_vertebra']['endplate_center']),
                'left_articular_tip_B': scale_coordinate(puncture_plan['lower_vertebra']['left_articular_tip_B']),
                'right_articular_tip_B': scale_coordinate(puncture_plan['lower_vertebra']['right_articular_tip_B'])
            }
        }

        return web_plan

    def generate_threejs_webpage(self, puncture_plans, output_dir="web_output", auto_open=True):
        """
        生成Three.js可视化网页

        Args:
            puncture_plans: 穿刺规划数据列表或字典
            output_dir: 输出目录
            auto_open: 是否自动打开浏览器
        """
        os.makedirs(output_dir, exist_ok=True)

        print("=" * 60)
        print("Generating Three.js Web Visualization")
        print("=" * 60)

        # 生成网格数据
        print("Generating vertebra mesh data...")
        mesh_data = self.generate_web_mesh_data(simplify_factor=3)

        # 转换穿刺规划数据
        print("Converting puncture plan data...")
        web_plans = {}

        if isinstance(puncture_plans, dict):
            # 如果是字典，直接处理
            for level, plan in puncture_plans.items():
                web_plan = self.convert_plan_to_web_format(plan)
                if web_plan:
                    web_plans[level] = web_plan
        elif isinstance(puncture_plans, list):
            # 如果是列表，按椎间隙分类
            for plan in puncture_plans:
                if plan:
                    web_plan = self.convert_plan_to_web_format(plan)
                    if web_plan:
                        web_plans[web_plan['target_level']] = web_plan
        else:
            # 单个规划
            web_plan = self.convert_plan_to_web_format(puncture_plans)
            if web_plan:
                web_plans[web_plan['target_level']] = web_plan

        # 保存数据文件
        mesh_file = os.path.join(output_dir, 'vertebrae_meshes.json')
        plan_file = os.path.join(output_dir, 'puncture_plans.json')

        with open(mesh_file, 'w') as f:
            json.dump(mesh_data, f, indent=2)
        print(f"Saved mesh data to: {mesh_file}")

        with open(plan_file, 'w') as f:
            json.dump(web_plans, f, indent=2)
        print(f"Saved plan data to: {plan_file}")

        # 生成HTML文件
        html_file = os.path.join(output_dir, 'index.html')
        self._generate_html_file(html_file, mesh_data, web_plans)
        print(f"Generated HTML file: {html_file}")

        # 生成JavaScript文件
        js_file = os.path.join(output_dir, 'spine_viewer.js')
        self._generate_js_file(js_file)
        print(f"Generated JavaScript file: {js_file}")

        print(f"\nWeb visualization generated successfully!")
        print(f"Output directory: {os.path.abspath(output_dir)}")
        print(f"Open {html_file} in your browser to view the visualization.")

        # 自动打开浏览器
        if auto_open:
            try:
                file_url = f"file://{os.path.abspath(html_file)}"
                webbrowser.open(file_url)
                print(f"Opening browser: {file_url}")
            except Exception as e:
                print(f"Could not open browser automatically: {e}")
                print("Please open the HTML file manually.")

    def _generate_html_file(self, html_file, mesh_data, web_plans):
        """生成HTML文件"""

        vertebrae_list = list(mesh_data.keys())
        plans_list = list(web_plans.keys())

        html_content = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spine Puncture Planning - 3D Visualization</title>
    <style>
        body {{
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }}

        #container {{
            position: relative;
            width: 100vw;
            height: 100vh;
        }}

        #controls {{
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            min-width: 250px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 1000;
        }}

        #controls h3 {{
            margin-top: 0;
            color: #4ecdc4;
        }}

        .control-group {{
            margin-bottom: 15px;
        }}

        .control-group label {{
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }}

        .control-group input[type="checkbox"] {{
            margin-right: 8px;
        }}

        .control-group input[type="range"] {{
            width: 100%;
        }}

        .control-group select, .control-group button {{
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 4px;
            background: #333;
            color: white;
        }}

        .control-group button {{
            background: #4ecdc4;
            cursor: pointer;
            margin-top: 5px;
        }}

        .control-group button:hover {{
            background: #45b7d1;
        }}

        #info {{
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            max-width: 300px;
        }}

        .legend {{
            margin-top: 10px;
        }}

        .legend-item {{
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }}

        .legend-color {{
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }}

        #loading {{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 2000;
        }}

        .hidden {{
            display: none !important;
        }}
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <h3>Loading 3D Visualization...</h3>
            <p>Please wait while the spine model is being rendered.</p>
        </div>

        <div id="controls">
            <h3>Spine Puncture Planning</h3>

            <div class="control-group">
                <label>Show Vertebrae:</label>
                <div>'''

        # 添加椎体显示控制
        for vertebra in ['L3', 'L4', 'L5', 'S1']:
            checked = 'checked' if vertebra in vertebrae_list else ''
            html_content += f'''
                    <input type="checkbox" id="show{vertebra}" {checked}> {vertebra}'''

        html_content += f'''
                </div>
            </div>

            <div class="control-group">
                <label for="punctureLevel">Puncture Planning:</label>
                <select id="punctureLevel">
                    <option value="">Select Level</option>'''

        # 添加穿刺规划选项
        for plan_level in plans_list:
            html_content += f'''
                    <option value="{plan_level}">{plan_level}</option>'''

        html_content += f'''
                </select>
            </div>

            <div class="control-group">
                <label for="opacity">Vertebra Opacity:</label>
                <input type="range" id="opacity" min="0.1" max="1.0" step="0.1" value="0.7">
                <span id="opacityValue">0.7</span>
            </div>

            <div class="control-group">
                <button id="resetView">Reset View</button>
            </div>
        </div>

        <div id="info">
            <h4>3D Spine Puncture Planning</h4>
            <p>Use mouse to rotate, scroll to zoom</p>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff6b6b;"></div>
                    <span>L3 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ecdc4;"></div>
                    <span>L4 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45b7d1;"></div>
                    <span>L5 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f9ca24;"></div>
                    <span>S1 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff0000;"></div>
                    <span>Target Point A</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #00ff00;"></div>
                    <span>Articular Process B</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- OrbitControls -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>

    <!-- Main Script -->
    <script src="spine_viewer.js"></script>

    <!-- Initialize with data -->
    <script>
        // Load data when page loads
        window.addEventListener('load', function() {{
            const viewer = new SpineViewer();
            viewer.loadDataFromFiles('vertebrae_meshes.json', 'puncture_plans.json');
        }});
    </script>
</body>
</html>'''

        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def _generate_js_file(self, js_file):
        """生成JavaScript文件"""

        js_content = '''/**
 * Spine Puncture Planning 3D Viewer
 * Generated automatically by PuncturePlanner
 */

class SpineViewer {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;

        this.vertebraMeshes = {};
        this.puncturePoints = {};
        this.currentPlan = null;

        this.colors = {
            L3: 0xff6b6b,
            L4: 0x4ecdc4,
            L5: 0x45b7d1,
            S1: 0xf9ca24
        };

        this.init();
        this.setupEventListeners();
    }

    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a1a);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 0, 200);

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        document.getElementById('container').appendChild(this.renderer.domElement);

        // Create controls
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;

        // Add lighting
        this.setupLighting();

        // Start render loop
        this.animate();

        // Hide loading screen
        document.getElementById('loading').classList.add('hidden');
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Additional lights for better visibility
        const light1 = new THREE.DirectionalLight(0xffffff, 0.3);
        light1.position.set(-100, -100, -50);
        this.scene.add(light1);

        const light2 = new THREE.DirectionalLight(0xffffff, 0.3);
        light2.position.set(0, 100, -100);
        this.scene.add(light2);
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
    }

    setupEventListeners() {
        // Vertebra visibility controls
        ['L3', 'L4', 'L5', 'S1'].forEach(vertebra => {
            const checkbox = document.getElementById(`show${vertebra}`);
            if (checkbox) {
                checkbox.addEventListener('change', (e) => {
                    this.toggleVertebra(vertebra, e.target.checked);
                });
            }
        });

        // Puncture planning selection
        const planSelect = document.getElementById('punctureLevel');
        if (planSelect) {
            planSelect.addEventListener('change', (e) => {
                this.showPuncturePlan(e.target.value);
            });
        }

        // Opacity control
        const opacitySlider = document.getElementById('opacity');
        const opacityValue = document.getElementById('opacityValue');
        if (opacitySlider && opacityValue) {
            opacitySlider.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                opacityValue.textContent = value.toFixed(1);
                this.setOpacity(value);
            });
        }

        // Reset view
        const resetButton = document.getElementById('resetView');
        if (resetButton) {
            resetButton.addEventListener('click', () => {
                this.resetView();
            });
        }

        // Window resize
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
    }'''

        # 读取模板文件的其余部分
        template_file = 'threejs_template.js'
        if os.path.exists(template_file):
            with open(template_file, 'r', encoding='utf-8') as f:
                js_content += f.read()
        else:
            # 如果模板文件不存在，添加基本的其余方法
            js_content += '''

    async loadDataFromFiles(meshFile, planFile) {
        try {
            document.getElementById('loading').classList.remove('hidden');

            const meshResponse = await fetch(meshFile);
            const meshData = await meshResponse.json();
            this.createVertebraeFromMeshData(meshData);

            const planResponse = await fetch(planFile);
            const planData = await planResponse.json();
            this.puncturePoints = planData;

        } catch (error) {
            console.error('Failed to load data:', error);
            alert('Failed to load data: ' + error.message);
        } finally {
            document.getElementById('loading').classList.add('hidden');
        }
    }

    createVertebraeFromMeshData(meshData) {
        Object.keys(meshData).forEach(label => {
            if (this.colors[label]) {
                const data = meshData[label];
                const geometry = new THREE.BufferGeometry();
                geometry.setAttribute('position', new THREE.Float32BufferAttribute(data.vertices, 3));
                if (data.faces) {
                    geometry.setIndex(data.faces);
                }
                geometry.computeVertexNormals();

                const material = new THREE.MeshLambertMaterial({
                    color: this.colors[label],
                    transparent: true,
                    opacity: 0.7,
                    side: THREE.DoubleSide
                });

                const mesh = new THREE.Mesh(geometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;

                this.vertebraMeshes[label] = mesh;
                this.scene.add(mesh);
            }
        });
    }

    toggleVertebra(vertebra, visible) {
        if (this.vertebraMeshes[vertebra]) {
            this.vertebraMeshes[vertebra].visible = visible;
        }
    }

    setOpacity(opacity) {
        Object.values(this.vertebraMeshes).forEach(mesh => {
            mesh.material.opacity = opacity;
        });
    }

    showPuncturePlan(level) {
        this.clearPuncturePoints();

        if (!level || !this.puncturePoints[level]) {
            return;
        }

        const plan = this.puncturePoints[level];

        if (plan.target_point_A) {
            const targetGeometry = new THREE.SphereGeometry(0.5, 16, 16);
            const targetMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
            const targetMesh = new THREE.Mesh(targetGeometry, targetMaterial);
            targetMesh.position.set(...plan.target_point_A);
            targetMesh.name = 'target_point_A';
            this.scene.add(targetMesh);
        }

        const bPointGeometry = new THREE.SphereGeometry(0.3, 12, 12);
        const bPointMaterial = new THREE.MeshLambertMaterial({ color: 0x00ff00 });

        ['upper_vertebra', 'lower_vertebra'].forEach(vertebra => {
            const vertebraData = plan[vertebra];
            if (vertebraData) {
                ['left_articular_tip_B', 'right_articular_tip_B'].forEach(tip => {
                    if (vertebraData[tip]) {
                        const bMesh = new THREE.Mesh(bPointGeometry, bPointMaterial);
                        bMesh.position.set(...vertebraData[tip]);
                        bMesh.name = `${vertebra}_${tip}`;
                        this.scene.add(bMesh);
                    }
                });
            }
        });
    }

    clearPuncturePoints() {
        const objectsToRemove = [];
        this.scene.traverse((object) => {
            if (object.name && (
                object.name.includes('target_point') ||
                object.name.includes('articular_tip') ||
                object.name.includes('path_')
            )) {
                objectsToRemove.push(object);
            }
        });

        objectsToRemove.forEach(object => {
            this.scene.remove(object);
        });
    }

    resetView() {
        this.camera.position.set(0, 0, 200);
        this.controls.reset();
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
}

window.SpineViewer = SpineViewer;'''

        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(js_content)

def main():
    # 使用示例 - 使用修正后的分割文件
    planner = PuncturePlanner('spine_pelvis_output_final.nii.gz')

    print("=" * 60)
    print("Spine Puncture Planning System")
    print("=" * 60)

    # 规划L4/L5椎间隙穿刺
    print("\nPlanning L4/L5 intervertebral puncture...")
    l4l5_plan = planner.plan_puncture_path('L4L5')
    if l4l5_plan:
        print("L4/L5 Puncture Plan Results:")
        print(f"  Target Point A: {l4l5_plan['target_point_A']}")
    else:
        print("L4/L5 planning failed")

    # 规划L5/S1椎间隙穿刺
    print("\nPlanning L5/S1 intervertebral puncture...")
    l5s1_plan = planner.plan_puncture_path('L5S1')
    if l5s1_plan:
        print("L5/S1 Puncture Plan Results:")
        print(f"  Target Point A: {l5s1_plan['target_point_A']}")
    else:
        print("L5/S1 planning failed")

    # 收集所有规划结果
    plans = {}
    if l4l5_plan:
        plans['L4L5'] = l4l5_plan
    if l5s1_plan:
        plans['L5S1'] = l5s1_plan

    if plans:
        print("\nGenerating Three.js Web Visualization...")

        # 生成Three.js网页
        planner.generate_threejs_webpage(
            puncture_plans=plans,
            output_dir="web_output",
            auto_open=True
        )

        # 可选：也显示matplotlib可视化
        print("\nOptional: Showing matplotlib visualization...")
        for level, plan in plans.items():
            print(f"Displaying {level} plan...")
            planner.visualize_puncture_plan(plan, show_vertebrae=True)
    else:
        print("No valid puncture plans generated")

    print("\nPuncture planning completed!")

if __name__ == "__main__":
    main()