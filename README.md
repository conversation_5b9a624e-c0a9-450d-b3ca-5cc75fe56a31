# 脊椎穿刺规划系统

这是一个完整的脊椎穿刺规划系统，包含分割、规划计算和3D Web可视化功能。

## 系统概述

本系统主要解决了以下问题：
1. **分割标签映射错误** - 修正了TotalSegmentator输出的标签映射
2. **穿刺规划计算** - 实现了L4/L5和L5/S1椎间隙的穿刺路径规划
3. **3D可视化展示** - 提供了基于Three.js的Web可视化界面

## 主要功能

- ✅ **脊椎骨盆分割**: 使用TotalSegmentator进行自动分割
- ✅ **标签映射修正**: 将原始标签映射为穿刺规划所需的L3(22)、L4(23)、L5(24)、S1(27)
- ✅ **穿刺路径规划**: 计算椎间隙穿刺的目标点A和关节突尖部B点
- ✅ **3D Web可视化**: 交互式3D展示椎体和穿刺规划点
- ✅ **完整工作流程**: 一键运行从分割到可视化的完整流程

## 文件结构

```
SegSpine/
├── spine_pelvis_segmentation.py    # 脊椎骨盆分割
├── fix_segmentation_labels.py      # 标签映射修正
├── puncture_planning.py            # 穿刺路径规划
├── generate_web_data.py            # Web数据生成
├── run_complete_workflow.py        # 完整工作流程
├── web_viewer/                     # Web可视化界面
│   ├── index.html                  # 主页面
│   ├── spine_viewer.js             # 3D可视化逻辑
│   ├── server.py                   # Web服务器
│   └── README.md                   # Web界面说明
└── README.md                       # 本文档
```

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install TotalSegmentator nibabel numpy scipy matplotlib scikit-image
```

### 2. 运行完整工作流程

```bash
# 一键运行（包含Web可视化）
python run_complete_workflow.py demo.nii.gz -o output --start-server

# 或者不生成Web数据
python run_complete_workflow.py demo.nii.gz -o output --no-web
```

### 3. 单独运行各个步骤

```bash
# 1. 分割
python spine_pelvis_segmentation.py demo.nii.gz raw_segmentation.nii.gz

# 2. 修正标签
python fix_segmentation_labels.py raw_segmentation.nii.gz fixed_segmentation.nii.gz

# 3. 穿刺规划
python puncture_planning.py  # 需要修改代码中的文件路径

# 4. 生成Web数据
python generate_web_data.py fixed_segmentation.nii.gz -p l4l5_plan.json l5s1_plan.json -o web_data

# 5. 启动Web服务器
cd web_viewer && python server.py
```

## 问题解决方案

### 原始问题
- **问题**: temp文件中包含完整分割结果，但没有生成正确的spine_pelvis_output.nii.gz
- **原因**: TotalSegmentator的标签映射与穿刺规划程序期望的标签不匹配

### 解决方案
1. **标签映射分析**: 通过分析体素数量确定了正确的椎体标签对应关系
   - 原始标签28 → L3(22)
   - 原始标签29 → L4(23)  
   - 原始标签30 → L5(24)
   - 原始标签27 → S1(27)

2. **修正脚本**: 创建了`fix_segmentation_labels.py`来自动修正标签映射

3. **验证机制**: 确保所有必需的标签都正确映射

## Web可视化功能

### 特性
- 🎯 **3D椎体显示**: 显示L3、L4、L5、S1的3D模型
- 📍 **穿刺点可视化**: 显示目标点A（红色）和关节突尖部B点（绿色）
- 🎮 **交互控制**: 鼠标旋转、缩放、透明度调节
- 📊 **多椎间隙支持**: L4/L5和L5/S1椎间隙规划切换

### 使用方法
1. 启动Web服务器后，浏览器会自动打开
2. 在控制面板中加载分割文件和规划文件
3. 使用鼠标交互查看3D模型
4. 切换不同椎间隙的穿刺规划

## 技术细节

### 标签映射逻辑
```python
# 基于体素数量分析的映射关系
label_mapping = {
    28: 22,  # L3 (体素数量: 145905)
    29: 23,  # L4 (体素数量: 146126) 
    30: 24,  # L5 (体素数量: 136044)
    27: 27,  # S1/sacrum (体素数量: 150930)
}
```

### 穿刺规划算法
- **目标点A**: 椎间隙中心（上下椎体终板中心的中点）
- **关节突尖部B**: 椎体后部左右两侧的最外侧点
- **坐标转换**: 体素坐标转换为物理世界坐标

### Web数据格式
- **网格数据**: 使用Marching Cubes算法生成3D网格
- **坐标缩放**: 应用1/10缩放因子适配Web显示
- **网格简化**: 限制面数以提高渲染性能

## 输出结果

### 文件输出
- `raw_segmentation.nii.gz`: 原始分割结果
- `spine_pelvis_segmentation.nii.gz`: 修正后的分割结果
- `l4l5_puncture_plan.json`: L4/L5椎间隙穿刺规划
- `l5s1_puncture_plan.json`: L5/S1椎间隙穿刺规划
- `web_data/`: Web可视化数据目录

### 规划结果示例
```json
{
  "target_level": "L4L5",
  "target_point_A": [-0.419, -3.359, -99.569],
  "upper_vertebra": {
    "left_articular_tip_B": [...],
    "right_articular_tip_B": [...]
  },
  "lower_vertebra": {
    "left_articular_tip_B": [...],
    "right_articular_tip_B": [...]
  }
}
```

## 故障排除

### 常见问题
1. **分割失败**: 检查TotalSegmentator安装和GPU/CPU配置
2. **标签缺失**: 确认输入图像包含目标椎体区域
3. **Web显示异常**: 检查浏览器兼容性和JavaScript控制台错误
4. **服务器启动失败**: 确认端口未被占用

### 调试建议
- 查看各步骤的输出日志
- 检查中间文件的标签分布
- 使用浏览器开发者工具调试Web界面

## 扩展功能

可以考虑添加的功能：
- 更多椎间隙支持（L3/L4等）
- 穿刺路径角度计算
- 安全区域分析
- 导出STL模型
- VR/AR支持

## 许可证

本项目仅供学术研究使用。
