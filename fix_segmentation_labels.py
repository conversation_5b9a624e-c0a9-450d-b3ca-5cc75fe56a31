#!/usr/bin/env python3
"""
修正脊椎骨盆分割标签的脚本

这个脚本用于修正TotalSegmentator输出的标签，使其符合穿刺规划程序的要求。
主要修正L3、L4、L5和S1的标签映射。
"""

import os
import nibabel as nib
import numpy as np
import argparse

def fix_segmentation_labels(input_path, output_path):
    """
    修正分割标签映射
    
    Args:
        input_path: 输入的分割文件路径
        output_path: 输出的修正文件路径
    """
    print(f"正在读取分割文件: {input_path}")
    
    # 读取分割文件
    img = nib.load(input_path)
    data = img.get_fdata().astype(np.uint8)
    
    print(f"文件形状: {data.shape}")
    unique_labels = np.unique(data)
    print(f"原始标签数量: {len(unique_labels)}")
    print(f"原始标签: {sorted(unique_labels)}")
    
    # 分析各标签的体素数量
    print("\n标签体素数量分析:")
    label_counts = []
    for label in unique_labels:
        if label > 0:  # 忽略背景
            count = np.sum(data == label)
            label_counts.append((label, count))
    
    # 按体素数量排序
    label_counts.sort(key=lambda x: x[1], reverse=True)
    for label, count in label_counts:
        print(f"  标签 {int(label)}: {count} 体素")
    
    # 创建修正后的数据
    corrected_data = np.zeros_like(data)
    
    # 基于TotalSegmentator的实际输出进行标签映射
    # 根据体素数量和解剖位置推断的映射关系
    label_mapping = {
        # 椎体标签映射 (基于体素数量分析)
        28: 22,  # L3 (体素数量约145905)
        29: 23,  # L4 (体素数量约146126) 
        30: 24,  # L5 (体素数量约136044)
        27: 27,  # S1/sacrum (体素数量约150930)
        
        # 可选：保留其他重要的解剖结构
        # 31: 19,  # T12 (如果需要)
        # 32: 18,  # T11 (如果需要)
        # 25: 25,  # 髂骨等骨盆结构
        # 26: 26,  # 骨盆结构
    }
    
    print(f"\n应用标签映射:")
    mapped_count = 0
    for old_label, new_label in label_mapping.items():
        mask = data == old_label
        if np.any(mask):
            corrected_data[mask] = new_label
            count = np.sum(mask)
            mapped_count += 1
            print(f"  {old_label} -> {new_label}: {count} 体素")
        else:
            print(f"  {old_label} -> {new_label}: 未找到原始标签")
    
    print(f"\n成功映射 {mapped_count} 个标签")
    
    # 保存修正后的文件
    corrected_img = nib.Nifti1Image(corrected_data, img.affine, img.header)
    nib.save(corrected_img, output_path)
    
    print(f"修正后的文件已保存到: {output_path}")
    
    # 验证结果
    final_labels = np.unique(corrected_data)
    print(f"最终标签: {sorted(final_labels[final_labels > 0])}")
    
    # 检查穿刺规划需要的标签
    needed_labels = [22, 23, 24, 27]  # L3, L4, L5, S1
    print(f"\n穿刺规划标签验证:")
    all_found = True
    for label in needed_labels:
        if label in final_labels:
            count = np.sum(corrected_data == label)
            print(f"  ✓ 标签 {label}: 存在，{count} 体素")
        else:
            print(f"  ✗ 标签 {label}: 缺失")
            all_found = False
    
    if all_found:
        print("\n✓ 所有必需的标签都已正确映射！")
    else:
        print("\n✗ 部分标签缺失，请检查输入文件或调整映射关系")
    
    return all_found

def main():
    parser = argparse.ArgumentParser(description="修正脊椎骨盆分割标签")
    parser.add_argument("input", help="输入分割文件路径")
    parser.add_argument("output", help="输出修正文件路径")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return
    
    success = fix_segmentation_labels(args.input, args.output)
    
    if success:
        print(f"\n标签修正完成！现在可以使用 {args.output} 进行穿刺规划。")
    else:
        print(f"\n标签修正存在问题，请检查输入文件。")

if __name__ == "__main__":
    main()
