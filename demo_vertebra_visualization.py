#!/usr/bin/env python3
"""
椎骨可视化演示脚本

这个脚本演示了如何使用增强的穿刺规划可视化功能，
包括椎骨3D模型的显示。
"""

import os
import sys
from puncture_planning import PuncturePlanner

def demo_vertebra_visualization():
    """演示椎骨可视化功能"""
    
    # 检查分割文件是否存在
    segmentation_file = 'spine_pelvis_output_final.nii.gz'
    if not os.path.exists(segmentation_file):
        print(f"错误: 分割文件不存在: {segmentation_file}")
        print("请先运行脊椎分割流程生成分割文件")
        return
    
    print("=" * 70)
    print("椎骨可视化演示")
    print("=" * 70)
    print(f"使用分割文件: {segmentation_file}")
    
    # 创建穿刺规划器
    planner = PuncturePlanner(segmentation_file)
    
    # 演示1: 带椎骨的完整可视化
    print("\n1. 完整可视化演示 (包含椎骨3D模型)")
    print("-" * 50)
    
    # L4/L5椎间隙
    print("规划 L4/L5 椎间隙穿刺...")
    l4l5_plan = planner.plan_puncture_path('L4L5')
    if l4l5_plan:
        print(f"目标点 A: {l4l5_plan['target_point_A']}")
        print("显示带椎骨的3D可视化...")
        planner.visualize_puncture_plan(l4l5_plan, show_vertebrae=True)
    else:
        print("L4/L5 规划失败")
    
    # L5/S1椎间隙
    print("\n规划 L5/S1 椎间隙穿刺...")
    l5s1_plan = planner.plan_puncture_path('L5S1')
    if l5s1_plan:
        print(f"目标点 A: {l5s1_plan['target_point_A']}")
        print("显示带椎骨的3D可视化...")
        planner.visualize_puncture_plan(l5s1_plan, show_vertebrae=True)
    else:
        print("L5/S1 规划失败")
    
    # 演示2: 简化可视化（仅显示点）
    print("\n2. 简化可视化演示 (仅显示穿刺点)")
    print("-" * 50)
    
    if l4l5_plan:
        print("L4/L5 简化可视化...")
        planner.visualize_puncture_plan_simple(l4l5_plan)
    
    if l5s1_plan:
        print("L5/S1 简化可视化...")
        planner.visualize_puncture_plan_simple(l5s1_plan)
    
    print("\n演示完成！")
    print("\n功能说明:")
    print("- 红色球: 穿刺目标点 A")
    print("- 蓝色/绿色三角: 关节突尖部 B 点")
    print("- 半透明表面: 椎骨3D模型")
    print("- 红色虚线: 穿刺路径示意")

def demo_individual_vertebra():
    """演示单个椎体的可视化"""
    
    segmentation_file = 'spine_pelvis_output_final.nii.gz'
    if not os.path.exists(segmentation_file):
        print(f"错误: 分割文件不存在: {segmentation_file}")
        return
    
    print("\n" + "=" * 70)
    print("单个椎体可视化演示")
    print("=" * 70)
    
    planner = PuncturePlanner(segmentation_file)
    
    # 测试单个椎体表面生成
    vertebra_labels = {22: 'L3', 23: 'L4', 24: 'L5', 27: 'S1'}
    
    for label, name in vertebra_labels.items():
        print(f"\n生成 {name} 椎体表面...")
        vertices, faces = planner.generate_vertebra_surface(label, simplify_factor=5)
        
        if vertices is not None and faces is not None:
            print(f"  {name}: {len(vertices)} 顶点, {len(faces)} 面")
        else:
            print(f"  {name}: 生成失败")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--individual":
        demo_individual_vertebra()
    else:
        demo_vertebra_visualization()
