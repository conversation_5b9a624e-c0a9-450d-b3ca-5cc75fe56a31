# 脊椎穿刺规划3D Web可视化

这是一个基于Three.js的Web应用，用于可视化脊椎分割结果和穿刺规划。

## 功能特性

- **3D椎体可视化**: 显示L3、L4、L5、S1椎体的3D模型
- **穿刺规划展示**: 可视化穿刺目标点A和关节突尖部B点
- **交互式控制**: 鼠标拖拽旋转、滚轮缩放、透明度调节
- **多椎间隙支持**: 支持L4/L5和L5/S1椎间隙规划
- **实时切换**: 可以实时显示/隐藏不同椎体和穿刺规划

## 文件结构

```
web_viewer/
├── index.html          # 主页面
├── spine_viewer.js     # 主要JavaScript代码
├── server.py          # 简单Web服务器
└── README.md          # 说明文档
```

## 使用方法

### 1. 生成Web数据

首先需要从分割结果生成Web展示所需的数据：

```bash
# 生成Web数据
python generate_web_data.py spine_pelvis_output_fixed.nii.gz -p l4l5_puncture_plan.json l5s1_puncture_plan.json -o web_data

# 或者使用完整工作流程（自动生成Web数据）
python run_complete_workflow.py demo.nii.gz -o output --start-server
```

### 2. 启动Web服务器

```bash
# 进入web_viewer目录
cd web_viewer

# 启动服务器
python server.py

# 或指定端口
python server.py -p 8080
```

### 3. 在浏览器中查看

服务器启动后会自动打开浏览器，或手动访问：
- http://localhost:8000

## 操作说明

### 控制面板

- **分割文件**: 上传椎体分割的NIfTI文件或JSON网格数据
- **穿刺规划文件**: 上传穿刺规划的JSON文件
- **显示椎体**: 勾选框控制各椎体的显示/隐藏
- **穿刺规划**: 下拉菜单选择要显示的椎间隙规划
- **透明度**: 滑块调节椎体的透明度
- **重置视角**: 恢复到默认视角

### 鼠标操作

- **左键拖拽**: 旋转视角
- **滚轮**: 缩放
- **右键拖拽**: 平移视角

### 颜色说明

- **红色**: L3椎体
- **青色**: L4椎体  
- **蓝色**: L5椎体
- **黄色**: S1椎体
- **红色球**: 穿刺目标点A
- **绿色球**: 关节突尖部B点

## 数据格式

### 网格数据格式 (vertebrae_meshes.json)

```json
{
  "L3": {
    "vertices": [x1, y1, z1, x2, y2, z2, ...],
    "faces": [i1, j1, k1, i2, j2, k2, ...],
    "vertex_count": 1000,
    "face_count": 500
  },
  ...
}
```

### 穿刺规划数据格式 (puncture_plans.json)

```json
{
  "L4L5": {
    "target_level": "L4L5",
    "target_point_A": [x, y, z],
    "upper_vertebra": {
      "endplate_center": [x, y, z],
      "left_articular_tip_B": [x, y, z],
      "right_articular_tip_B": [x, y, z]
    },
    "lower_vertebra": {
      "endplate_center": [x, y, z],
      "left_articular_tip_B": [x, y, z],
      "right_articular_tip_B": [x, y, z]
    }
  }
}
```

## 技术栈

- **Three.js**: 3D图形渲染
- **HTML5/CSS3**: 用户界面
- **JavaScript ES6+**: 应用逻辑
- **Python**: 数据生成和Web服务器

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 故障排除

### 常见问题

1. **网格不显示**
   - 检查网格数据文件格式是否正确
   - 确认顶点和面数据完整

2. **穿刺点位置不对**
   - 检查坐标系转换是否正确
   - 确认缩放因子设置

3. **加载缓慢**
   - 网格数据过大，考虑简化网格
   - 检查网络连接

4. **服务器启动失败**
   - 端口被占用，尝试其他端口
   - 检查Python环境

### 调试模式

在浏览器中按F12打开开发者工具，查看控制台输出获取详细错误信息。

## 扩展功能

可以考虑添加的功能：

- 切片视图显示
- 测量工具
- 导出功能
- 动画演示
- VR/AR支持

## 许可证

本项目仅供学术研究使用。
