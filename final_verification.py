#!/usr/bin/env python3
"""
最终验证坐标修复的脚本
"""

import os
import json
import numpy as np

def final_verification():
    """最终验证坐标修复是否成功"""
    
    print("=" * 80)
    print("Final Verification of Coordinate Fix")
    print("=" * 80)
    
    # 检查服务器版本的数据
    print("\n1. Checking Server Version Data...")
    
    if not os.path.exists('threejs_output/puncture_plans.json'):
        print("Error: Server version data not found")
        return False
    
    with open('threejs_output/puncture_plans.json', 'r') as f:
        server_plans = json.load(f)
    
    with open('threejs_output/vertebrae_meshes.json', 'r') as f:
        mesh_data = json.load(f)
    
    # 计算椎骨中心
    vertebra_centers = {}
    for vertebra in ['L3', 'L4', 'L5', 'S1']:
        vertices = mesh_data[vertebra]['vertices']
        x_coords = [vertices[i] for i in range(0, len(vertices), 3)]
        y_coords = [vertices[i] for i in range(1, len(vertices), 3)]
        z_coords = [vertices[i] for i in range(2, len(vertices), 3)]
        
        center = [np.mean(x_coords), np.mean(y_coords), np.mean(z_coords)]
        vertebra_centers[vertebra] = center
        print(f"   {vertebra} center: [{center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f}]")
    
    # 检查穿刺点位置
    print("\n2. Checking Puncture Point Positions...")
    
    success = True
    
    for level, plan in server_plans.items():
        target = plan['target_point_A']
        print(f"\n   {level} target: [{target[0]:.2f}, {target[1]:.2f}, {target[2]:.2f}]")
        
        # 计算与相关椎骨的距离
        if level == 'L4L5':
            relevant_vertebrae = ['L4', 'L5']
        elif level == 'L5S1':
            relevant_vertebrae = ['L5', 'S1']
        else:
            relevant_vertebrae = ['L5']
        
        min_distance = float('inf')
        for vertebra in relevant_vertebrae:
            center = vertebra_centers[vertebra]
            distance = np.sqrt(sum((target[i] - center[i])**2 for i in range(3)))
            print(f"     Distance to {vertebra}: {distance:.2f}")
            min_distance = min(min_distance, distance)
        
        # 检查距离是否合理（应该在5个单位以内）
        if min_distance <= 5.0:
            print(f"     ✅ Position is reasonable (min distance: {min_distance:.2f})")
        else:
            print(f"     ❌ Position is too far (min distance: {min_distance:.2f})")
            success = False
    
    # 检查L5中心设置
    print("\n3. Checking L5 Center Configuration...")
    
    html_file = 'threejs_output/index.html'
    if os.path.exists(html_file):
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 查找RENDERING_CENTER配置
        import re
        center_match = re.search(r'const RENDERING_CENTER = \[(.*?)\];', html_content)
        if center_match:
            center_str = center_match.group(1)
            center_values = [float(x.strip()) for x in center_str.split(',')]
            print(f"   HTML rendering center: {center_values}")
            
            # 与L5椎骨中心比较
            l5_center = vertebra_centers['L5']
            difference = np.array(center_values) - np.array(l5_center)
            distance = np.linalg.norm(difference)
            
            if distance < 0.1:
                print(f"   ✅ Rendering center matches L5 center (difference: {distance:.6f})")
            else:
                print(f"   ❌ Rendering center doesn't match L5 center (difference: {distance:.2f})")
                success = False
        else:
            print("   ❌ Could not find RENDERING_CENTER in HTML")
            success = False
    else:
        print("   ❌ HTML file not found")
        success = False
    
    # 检查嵌入式版本
    print("\n4. Checking Embedded Version...")
    
    embedded_file = 'spine_visualization_embedded.html'
    if os.path.exists(embedded_file):
        with open(embedded_file, 'r', encoding='utf-8') as f:
            embedded_content = f.read()
        
        # 查找L5_CENTER配置
        center_match = re.search(r'const L5_CENTER = \[(.*?)\];', embedded_content)
        if center_match:
            center_str = center_match.group(1)
            center_values = [float(x.strip()) for x in center_str.split(',')]
            print(f"   Embedded L5 center: {center_values}")
            
            # 与L5椎骨中心比较
            l5_center = vertebra_centers['L5']
            difference = np.array(center_values) - np.array(l5_center)
            distance = np.linalg.norm(difference)
            
            if distance < 0.1:
                print(f"   ✅ Embedded center matches L5 center (difference: {distance:.6f})")
            else:
                print(f"   ❌ Embedded center doesn't match L5 center (difference: {distance:.2f})")
                success = False
        else:
            print("   ❌ Could not find L5_CENTER in embedded HTML")
            success = False
    else:
        print("   ❌ Embedded HTML file not found")
        success = False
    
    # 总结
    print("\n5. Final Summary...")
    
    if success:
        print("🎉 ALL CHECKS PASSED!")
        print("   ✅ Puncture points are positioned correctly relative to vertebrae")
        print("   ✅ L5 center is correctly set as the rendering center")
        print("   ✅ Both embedded and server versions are properly configured")
        print("\n   The visualization should now show:")
        print("   - Vertebrae and puncture points in correct relative positions")
        print("   - Camera and controls centered on L5 vertebra")
        print("   - Proper scale and proportions")
    else:
        print("❌ SOME CHECKS FAILED!")
        print("   There are still issues that need to be addressed.")
    
    return success

if __name__ == "__main__":
    final_verification()
