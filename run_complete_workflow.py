#!/usr/bin/env python3
"""
完整的脊椎穿刺规划工作流程

这个脚本整合了分割、标签修正和穿刺规划的完整流程。
"""

import os
import sys
import argparse
import subprocess
from spine_pelvis_segmentation import segment_spine_pelvis
from fix_segmentation_labels import fix_segmentation_labels
from puncture_planning import PuncturePlanner
from generate_web_data import WebDataGenerator

def run_complete_workflow(input_nii_path, output_dir="output", generate_web=True, start_server=False):
    """
    运行完整的穿刺规划工作流程

    Args:
        input_nii_path: 输入的NII.gz文件路径
        output_dir: 输出目录
        generate_web: 是否生成Web展示数据
        start_server: 是否启动Web服务器
    """
    print("=" * 60)
    print("脊椎穿刺规划完整工作流程")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义文件路径
    raw_segmentation = os.path.join(output_dir, "raw_segmentation.nii.gz")
    fixed_segmentation = os.path.join(output_dir, "spine_pelvis_segmentation.nii.gz")
    
    print(f"\n步骤 1: 运行脊椎骨盆分割")
    print(f"输入文件: {input_nii_path}")
    print(f"输出文件: {raw_segmentation}")
    
    try:
        segment_spine_pelvis(input_nii_path, raw_segmentation)
        print("✓ 分割完成")
    except Exception as e:
        print(f"✗ 分割失败: {e}")
        return False
    
    print(f"\n步骤 2: 修正标签映射")
    print(f"输入文件: {raw_segmentation}")
    print(f"输出文件: {fixed_segmentation}")
    
    try:
        success = fix_segmentation_labels(raw_segmentation, fixed_segmentation)
        if success:
            print("✓ 标签修正完成")
        else:
            print("✗ 标签修正存在问题")
            return False
    except Exception as e:
        print(f"✗ 标签修正失败: {e}")
        return False
    
    print(f"\n步骤 3: 运行穿刺规划")
    print(f"分割文件: {fixed_segmentation}")
    
    try:
        planner = PuncturePlanner(fixed_segmentation)
        
        # L4/L5椎间隙穿刺规划
        print(f"\n--- L4/L5椎间隙穿刺规划 ---")
        l4l5_plan = planner.plan_puncture_path('L4L5')
        if l4l5_plan:
            print("✓ L4/L5穿刺规划成功")
            print(f"目标点A坐标: {l4l5_plan['target_point_A']}")
            
            # 保存规划结果
            import json
            l4l5_output = os.path.join(output_dir, "l4l5_puncture_plan.json")
            with open(l4l5_output, 'w', encoding='utf-8') as f:
                # 转换numpy数组为列表以便JSON序列化
                plan_json = {}
                for key, value in l4l5_plan.items():
                    if isinstance(value, dict):
                        plan_json[key] = {}
                        for k, v in value.items():
                            if v is not None and hasattr(v, 'tolist'):
                                plan_json[key][k] = v.tolist()
                            else:
                                plan_json[key][k] = v
                    elif value is not None and hasattr(value, 'tolist'):
                        plan_json[key] = value.tolist()
                    else:
                        plan_json[key] = value
                json.dump(plan_json, f, indent=2, ensure_ascii=False)
            print(f"L4/L5规划结果已保存到: {l4l5_output}")
        else:
            print("✗ L4/L5穿刺规划失败")
        
        # L5/S1椎间隙穿刺规划
        print(f"\n--- L5/S1椎间隙穿刺规划 ---")
        l5s1_plan = planner.plan_puncture_path('L5S1')
        if l5s1_plan:
            print("✓ L5/S1穿刺规划成功")
            print(f"目标点A坐标: {l5s1_plan['target_point_A']}")
            
            # 保存规划结果
            l5s1_output = os.path.join(output_dir, "l5s1_puncture_plan.json")
            with open(l5s1_output, 'w', encoding='utf-8') as f:
                # 转换numpy数组为列表以便JSON序列化
                plan_json = {}
                for key, value in l5s1_plan.items():
                    if isinstance(value, dict):
                        plan_json[key] = {}
                        for k, v in value.items():
                            if v is not None and hasattr(v, 'tolist'):
                                plan_json[key][k] = v.tolist()
                            else:
                                plan_json[key][k] = v
                    elif value is not None and hasattr(value, 'tolist'):
                        plan_json[key] = value.tolist()
                    else:
                        plan_json[key] = value
                json.dump(plan_json, f, indent=2, ensure_ascii=False)
            print(f"L5/S1规划结果已保存到: {l5s1_output}")
        else:
            print("✗ L5/S1穿刺规划失败")
            
        print("✓ 穿刺规划完成")
        
    except Exception as e:
        print(f"✗ 穿刺规划失败: {e}")
        return False
    
        # 步骤 4: 生成Web展示数据
        if generate_web:
            print(f"\n步骤 4: 生成Web展示数据")
            web_data_dir = os.path.join(output_dir, "web_data")

            try:
                # 收集穿刺规划文件
                plan_files = []
                if l4l5_plan:
                    plan_files.append(os.path.join(output_dir, "l4l5_puncture_plan.json"))
                if l5s1_plan:
                    plan_files.append(os.path.join(output_dir, "l5s1_puncture_plan.json"))

                # 生成Web数据
                web_generator = WebDataGenerator()
                web_generator.generate_complete_web_data(
                    fixed_segmentation,
                    plan_files,
                    web_data_dir
                )

                print("✓ Web展示数据生成完成")

                # 启动Web服务器
                if start_server:
                    print(f"\n步骤 5: 启动Web服务器")
                    try:
                        import webbrowser
                        import time

                        # 复制Web查看器文件到输出目录
                        import shutil
                        web_viewer_src = "web_viewer"
                        web_viewer_dst = os.path.join(output_dir, "viewer")

                        if os.path.exists(web_viewer_src):
                            shutil.copytree(web_viewer_src, web_viewer_dst, dirs_exist_ok=True)

                            # 启动服务器
                            print("正在启动Web服务器...")
                            print("请在浏览器中查看3D可视化结果")

                            # 这里可以添加启动服务器的代码
                            print(f"Web查看器已复制到: {web_viewer_dst}")
                            print("请运行以下命令启动服务器:")
                            print(f"cd {web_viewer_dst} && python server.py")

                    except Exception as e:
                        print(f"启动Web服务器时出错: {e}")

            except Exception as e:
                print(f"✗ Web数据生成失败: {e}")

    except Exception as e:
        print(f"✗ 穿刺规划失败: {e}")
        return False

    print(f"\n" + "=" * 60)
    print("工作流程完成！")
    print(f"输出目录: {output_dir}")
    print("生成的文件:")
    print(f"  - {raw_segmentation} (原始分割)")
    print(f"  - {fixed_segmentation} (修正后分割)")
    if l4l5_plan:
        print(f"  - {os.path.join(output_dir, 'l4l5_puncture_plan.json')} (L4/L5规划)")
    if l5s1_plan:
        print(f"  - {os.path.join(output_dir, 'l5s1_puncture_plan.json')} (L5/S1规划)")
    if generate_web:
        print(f"  - {os.path.join(output_dir, 'web_data/')} (Web展示数据)")
        if start_server:
            print(f"  - {os.path.join(output_dir, 'viewer/')} (Web查看器)")
    print("=" * 60)

    return True

def main():
    parser = argparse.ArgumentParser(description="运行完整的脊椎穿刺规划工作流程")
    parser.add_argument("input", help="输入NII.gz文件路径")
    parser.add_argument("-o", "--output", default="output", help="输出目录 (默认: output)")
    parser.add_argument("--no-web", action="store_true", help="不生成Web展示数据")
    parser.add_argument("--start-server", action="store_true", help="生成Web数据后启动服务器")

    args = parser.parse_args()

    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        sys.exit(1)

    success = run_complete_workflow(
        args.input,
        args.output,
        generate_web=not args.no_web,
        start_server=args.start_server
    )

    if not success:
        print("工作流程执行失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
