#!/usr/bin/env python3
"""
Three.js网页生成演示脚本

这个脚本演示了如何使用PuncturePlanner生成交互式的Three.js网页可视化。
"""

import os
import sys
from puncture_planning import PuncturePlanner

def demo_threejs_generation():
    """演示Three.js网页生成功能"""
    
    # 检查分割文件是否存在
    segmentation_file = 'spine_pelvis_output_final.nii.gz'
    if not os.path.exists(segmentation_file):
        print(f"Error: Segmentation file not found: {segmentation_file}")
        print("Please run the spine segmentation workflow first")
        return
    
    print("=" * 70)
    print("Three.js Web Visualization Generation Demo")
    print("=" * 70)
    print(f"Using segmentation file: {segmentation_file}")
    
    # 创建穿刺规划器
    planner = PuncturePlanner(segmentation_file)
    
    # 执行穿刺规划
    print("\n1. Performing puncture planning...")
    print("-" * 50)
    
    plans = {}
    
    # L4/L5椎间隙规划
    print("Planning L4/L5 intervertebral puncture...")
    l4l5_plan = planner.plan_puncture_path('L4L5')
    if l4l5_plan:
        plans['L4L5'] = l4l5_plan
        print(f"  ✓ L4/L5 Target Point A: {l4l5_plan['target_point_A']}")
    else:
        print("  ✗ L4/L5 planning failed")
    
    # L5/S1椎间隙规划
    print("Planning L5/S1 intervertebral puncture...")
    l5s1_plan = planner.plan_puncture_path('L5S1')
    if l5s1_plan:
        plans['L5S1'] = l5s1_plan
        print(f"  ✓ L5/S1 Target Point A: {l5s1_plan['target_point_A']}")
    else:
        print("  ✗ L5/S1 planning failed")
    
    if not plans:
        print("No valid puncture plans generated. Exiting.")
        return
    
    # 生成Three.js网页
    print("\n2. Generating Three.js Web Visualization...")
    print("-" * 50)
    
    output_dir = "threejs_visualization"
    
    try:
        planner.generate_threejs_webpage(
            puncture_plans=plans,
            output_dir=output_dir,
            auto_open=True
        )
        
        print(f"\n✓ Three.js visualization generated successfully!")
        print(f"  Output directory: {os.path.abspath(output_dir)}")
        print(f"  Main file: {os.path.join(output_dir, 'index.html')}")
        
        # 显示生成的文件
        print("\nGenerated files:")
        for file in os.listdir(output_dir):
            file_path = os.path.join(output_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  - {file} ({file_size:,} bytes)")
        
    except Exception as e:
        print(f"✗ Failed to generate Three.js visualization: {e}")
        return
    
    # 提供使用说明
    print("\n3. Usage Instructions")
    print("-" * 50)
    print("The generated web visualization includes:")
    print("  • Interactive 3D spine model with vertebrae")
    print("  • Puncture planning points (A and B points)")
    print("  • Control panel for toggling vertebrae visibility")
    print("  • Opacity control for vertebrae")
    print("  • Puncture plan selection dropdown")
    print("  • Mouse controls: drag to rotate, scroll to zoom")
    
    print("\nTo view the visualization:")
    print(f"  1. Open {os.path.join(output_dir, 'index.html')} in your browser")
    print("  2. Or use a local web server for better performance")
    
    print("\nBrowser should open automatically. If not, navigate to:")
    print(f"  file://{os.path.abspath(os.path.join(output_dir, 'index.html'))}")

def demo_custom_output():
    """演示自定义输出目录和选项"""
    
    segmentation_file = 'spine_pelvis_output_final.nii.gz'
    if not os.path.exists(segmentation_file):
        print(f"Error: Segmentation file not found: {segmentation_file}")
        return
    
    print("\n" + "=" * 70)
    print("Custom Output Demo")
    print("=" * 70)
    
    planner = PuncturePlanner(segmentation_file)
    
    # 只规划一个椎间隙
    print("Planning only L4/L5 puncture...")
    l4l5_plan = planner.plan_puncture_path('L4L5')
    
    if l4l5_plan:
        # 生成到自定义目录，不自动打开浏览器
        custom_dir = "custom_web_output"
        print(f"Generating to custom directory: {custom_dir}")
        
        planner.generate_threejs_webpage(
            puncture_plans=l4l5_plan,  # 单个规划
            output_dir=custom_dir,
            auto_open=False  # 不自动打开浏览器
        )
        
        print(f"✓ Custom visualization saved to: {os.path.abspath(custom_dir)}")
    else:
        print("Planning failed")

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--custom":
        demo_custom_output()
    else:
        demo_threejs_generation()

if __name__ == "__main__":
    main()
