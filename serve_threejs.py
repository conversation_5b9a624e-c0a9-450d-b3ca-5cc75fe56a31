#!/usr/bin/env python3
"""
本地服务器用于查看Three.js可视化

这个脚本启动一个简单的HTTP服务器来查看生成的Three.js可视化，
解决浏览器CORS限制问题。
"""

import os
import sys
import http.server
import socketserver
import webbrowser
import argparse
from pathlib import Path

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""

    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def start_server(port=8000, directory=None, open_browser=True):
    """启动Web服务器"""
    if directory:
        os.chdir(directory)

    handler = CORSHTTPRequestHandler

    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"Three.js Visualization Server Started!")
            print(f"URL: http://localhost:{port}")
            print(f"Serving directory: {os.getcwd()}")
            print("Press Ctrl+C to stop the server")

            if open_browser:
                webbrowser.open(f'http://localhost:{port}')

            httpd.serve_forever()

    except KeyboardInterrupt:
        print("\nServer stopped")
    except OSError as e:
        if e.errno == 48 or "already in use" in str(e).lower():
            print(f"Error: Port {port} is already in use, try another port")
            print(f"Example: python {sys.argv[0]} -p {port + 1}")
        else:
            print(f"Error starting server: {e}")

def find_visualization_directory():
    """查找可视化目录"""
    possible_dirs = [
        'threejs_visualization',
        'web_output',
        'custom_web_output'
    ]

    for dir_name in possible_dirs:
        if os.path.exists(dir_name) and os.path.exists(os.path.join(dir_name, 'index.html')):
            return dir_name

    return None

def main():
    parser = argparse.ArgumentParser(description="Serve Three.js spine visualization")
    parser.add_argument("-p", "--port", type=int, default=8000, help="Port number (default: 8000)")
    parser.add_argument("-d", "--directory", help="Directory to serve (default: auto-detect)")
    parser.add_argument("--no-browser", action="store_true", help="Don't open browser automatically")

    args = parser.parse_args()

    # 确定服务目录
    if args.directory:
        if not os.path.exists(args.directory):
            print(f"Error: Directory '{args.directory}' does not exist")
            return
        serve_dir = args.directory
    else:
        serve_dir = find_visualization_directory()
        if not serve_dir:
            print("Error: No visualization directory found")
            print("Please run the Three.js generation script first:")
            print("  python demo_threejs_generation.py")
            return

    print("=" * 60)
    print("Three.js Spine Visualization Server")
    print("=" * 60)
    print(f"Serving: {os.path.abspath(serve_dir)}")

    # 检查必要文件
    required_files = ['index.html', 'spine_viewer.js', 'vertebrae_meshes.json', 'puncture_plans.json']
    missing_files = []

    for file in required_files:
        if not os.path.exists(os.path.join(serve_dir, file)):
            missing_files.append(file)

    if missing_files:
        print(f"Warning: Missing files: {', '.join(missing_files)}")
        print("The visualization may not work properly")
    else:
        print("✓ All required files found")

    # 显示可用文件
    print("\nAvailable files:")
    for file in os.listdir(serve_dir):
        file_path = os.path.join(serve_dir, file)
        if os.path.isfile(file_path):
            file_size = os.path.getsize(file_path)
            print(f"  - {file} ({file_size:,} bytes)")

    print("\nInstructions:")
    print("1. The browser will open automatically")
    print("2. Use mouse to rotate the 3D view")
    print("3. Use scroll wheel to zoom")
    print("4. Use the control panel to toggle vertebrae and select puncture plans")
    print()

    # 启动服务器
    start_server(
        port=args.port,
        directory=serve_dir,
        open_browser=not args.no_browser
    )

if __name__ == "__main__":
    main()
