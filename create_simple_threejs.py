#!/usr/bin/env python3
"""
创建简化的Three.js可视化

这个脚本创建一个更简单、更可靠的Three.js可视化，
将数据直接嵌入HTML中以避免CORS问题。
"""

import os
import json
import webbrowser
from puncture_planning import PuncturePlanner

def create_embedded_threejs_visualization():
    """创建嵌入数据的Three.js可视化"""
    
    # 检查分割文件
    segmentation_file = 'spine_pelvis_output_final.nii.gz'
    if not os.path.exists(segmentation_file):
        print(f"Error: Segmentation file not found: {segmentation_file}")
        return
    
    print("=" * 60)
    print("Creating Embedded Three.js Visualization")
    print("=" * 60)
    
    # 创建规划器并执行规划
    planner = PuncturePlanner(segmentation_file)
    
    print("Performing puncture planning...")
    plans = {}
    
    # L4/L5规划
    l4l5_plan = planner.plan_puncture_path('L4L5')
    if l4l5_plan:
        plans['L4L5'] = planner.convert_plan_to_web_format(l4l5_plan)
        print("✓ L4/L5 planning completed")
    
    # L5/S1规划
    l5s1_plan = planner.plan_puncture_path('L5S1')
    if l5s1_plan:
        plans['L5S1'] = planner.convert_plan_to_web_format(l5s1_plan)
        print("✓ L5/S1 planning completed")
    
    if not plans:
        print("No valid plans generated")
        return
    
    # 生成简化的网格数据
    print("Generating simplified mesh data...")
    mesh_data = planner.generate_web_mesh_data(simplify_factor=10)  # 更大的简化因子
    
    # 创建嵌入式HTML
    output_file = "spine_visualization_embedded.html"
    create_embedded_html(output_file, mesh_data, plans)
    
    print(f"✓ Embedded visualization created: {output_file}")
    print(f"File size: {os.path.getsize(output_file):,} bytes")
    
    # 自动打开浏览器
    try:
        file_url = f"file://{os.path.abspath(output_file)}"
        webbrowser.open(file_url)
        print(f"Opening browser: {file_url}")
    except Exception as e:
        print(f"Could not open browser: {e}")
        print(f"Please open {output_file} manually")

def create_embedded_html(output_file, mesh_data, plans):
    """创建嵌入数据的HTML文件"""
    
    # 将数据转换为JavaScript格式
    mesh_data_js = json.dumps(mesh_data, indent=2)
    plans_js = json.dumps(plans, indent=2)
    
    html_content = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spine Puncture Planning - 3D Visualization</title>
    <style>
        body {{
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }}
        
        #container {{
            position: relative;
            width: 100vw;
            height: 100vh;
        }}
        
        #controls {{
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            min-width: 250px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 1000;
        }}
        
        #controls h3 {{
            margin-top: 0;
            color: #4ecdc4;
        }}
        
        .control-group {{
            margin-bottom: 15px;
        }}
        
        .control-group label {{
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }}
        
        .control-group input[type="checkbox"] {{
            margin-right: 8px;
        }}
        
        .control-group input[type="range"] {{
            width: 100%;
        }}
        
        .control-group select, .control-group button {{
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 4px;
            background: #333;
            color: white;
        }}
        
        .control-group button {{
            background: #4ecdc4;
            cursor: pointer;
            margin-top: 5px;
        }}
        
        .control-group button:hover {{
            background: #45b7d1;
        }}
        
        #info {{
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            max-width: 300px;
        }}
        
        .legend {{
            margin-top: 10px;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }}
        
        .legend-color {{
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }}
        
        #loading {{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 2000;
        }}
        
        .hidden {{
            display: none !important;
        }}
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <h3>Loading 3D Visualization...</h3>
            <p>Please wait while the spine model is being rendered.</p>
        </div>
        
        <div id="controls">
            <h3>Spine Puncture Planning</h3>
            
            <div class="control-group">
                <label>Show Vertebrae:</label>
                <div>
                    <input type="checkbox" id="showL3" checked> L3
                    <input type="checkbox" id="showL4" checked> L4
                    <input type="checkbox" id="showL5" checked> L5
                    <input type="checkbox" id="showS1" checked> S1
                </div>
            </div>
            
            <div class="control-group">
                <label for="punctureLevel">Puncture Planning:</label>
                <select id="punctureLevel">
                    <option value="">Select Level</option>'''
    
    # 添加穿刺规划选项
    for plan_level in plans.keys():
        html_content += f'''
                    <option value="{plan_level}">{plan_level}</option>'''
    
    html_content += f'''
                </select>
            </div>
            
            <div class="control-group">
                <label for="opacity">Vertebra Opacity:</label>
                <input type="range" id="opacity" min="0.1" max="1.0" step="0.1" value="0.7">
                <span id="opacityValue">0.7</span>
            </div>
            
            <div class="control-group">
                <button id="resetView">Reset View</button>
            </div>
        </div>
        
        <div id="info">
            <h4>3D Spine Puncture Planning</h4>
            <p>Use mouse to rotate, scroll to zoom</p>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff6b6b;"></div>
                    <span>L3 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ecdc4;"></div>
                    <span>L4 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45b7d1;"></div>
                    <span>L5 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f9ca24;"></div>
                    <span>S1 Vertebra</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff0000;"></div>
                    <span>Target Point A</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #00ff00;"></div>
                    <span>Articular Process B</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- OrbitControls -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    
    <!-- Embedded Data -->
    <script>
        // Embedded mesh data
        const MESH_DATA = {mesh_data_js};
        
        // Embedded plan data
        const PLAN_DATA = {plans_js};
    </script>
    
    <!-- Main Script -->
    <script>
        class SpineViewer {{
            constructor() {{
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                
                this.vertebraMeshes = {{}};
                this.puncturePoints = {{}};
                this.currentPlan = null;
                
                this.colors = {{
                    L3: 0xff6b6b,
                    L4: 0x4ecdc4,
                    L5: 0x45b7d1,
                    S1: 0xf9ca24
                }};
                
                this.init();
                this.setupEventListeners();
                this.loadEmbeddedData();
            }}
            
            init() {{
                // Create scene
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x1a1a1a);
                
                // Create camera
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.camera.position.set(0, 0, 200);
                
                // Create renderer
                this.renderer = new THREE.WebGLRenderer({{ antialias: true }});
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                
                document.getElementById('container').appendChild(this.renderer.domElement);
                
                // Create controls
                this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;
                
                // Add lighting
                this.setupLighting();
                
                // Start render loop
                this.animate();
            }}
            
            setupLighting() {{
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                this.scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(100, 100, 50);
                directionalLight.castShadow = true;
                this.scene.add(directionalLight);
                
                const light1 = new THREE.DirectionalLight(0xffffff, 0.3);
                light1.position.set(-100, -100, -50);
                this.scene.add(light1);
            }}
            
            animate() {{
                requestAnimationFrame(() => this.animate());
                this.controls.update();
                this.renderer.render(this.scene, this.camera);
            }}
            
            loadEmbeddedData() {{
                try {{
                    console.log('Loading embedded data...');
                    this.createVertebraeFromMeshData(MESH_DATA);
                    this.puncturePoints = PLAN_DATA;
                    console.log('Embedded data loaded successfully');
                    document.getElementById('loading').classList.add('hidden');
                }} catch (error) {{
                    console.error('Failed to load embedded data:', error);
                    alert('Failed to load visualization data: ' + error.message);
                }}
            }}
            
            createVertebraeFromMeshData(meshData) {{
                Object.keys(meshData).forEach(label => {{
                    if (this.colors[label]) {{
                        const data = meshData[label];
                        
                        if (data.vertices && data.vertices.length > 0) {{
                            const geometry = new THREE.BufferGeometry();
                            geometry.setAttribute('position', new THREE.Float32BufferAttribute(data.vertices, 3));
                            
                            if (data.faces && data.faces.length > 0) {{
                                geometry.setIndex(data.faces);
                            }}
                            
                            geometry.computeVertexNormals();

                            const material = new THREE.MeshLambertMaterial({{
                                color: this.colors[label],
                                transparent: true,
                                opacity: 0.7,
                                side: THREE.DoubleSide
                            }});

                            const mesh = new THREE.Mesh(geometry, material);
                            mesh.castShadow = true;
                            mesh.receiveShadow = true;

                            this.vertebraMeshes[label] = mesh;
                            this.scene.add(mesh);
                            
                            console.log(`Created ${{label}} vertebra with ${{data.vertex_count}} vertices`);
                        }}
                    }}
                }});
            }}
            
            setupEventListeners() {{
                // Vertebra visibility controls
                ['L3', 'L4', 'L5', 'S1'].forEach(vertebra => {{
                    const checkbox = document.getElementById(`show${{vertebra}}`);
                    if (checkbox) {{
                        checkbox.addEventListener('change', (e) => {{
                            this.toggleVertebra(vertebra, e.target.checked);
                        }});
                    }}
                }});
                
                // Puncture planning selection
                const planSelect = document.getElementById('punctureLevel');
                if (planSelect) {{
                    planSelect.addEventListener('change', (e) => {{
                        this.showPuncturePlan(e.target.value);
                    }});
                }}
                
                // Opacity control
                const opacitySlider = document.getElementById('opacity');
                const opacityValue = document.getElementById('opacityValue');
                if (opacitySlider && opacityValue) {{
                    opacitySlider.addEventListener('input', (e) => {{
                        const value = parseFloat(e.target.value);
                        opacityValue.textContent = value.toFixed(1);
                        this.setOpacity(value);
                    }});
                }}
                
                // Reset view
                const resetButton = document.getElementById('resetView');
                if (resetButton) {{
                    resetButton.addEventListener('click', () => {{
                        this.resetView();
                    }});
                }}
                
                // Window resize
                window.addEventListener('resize', () => {{
                    this.onWindowResize();
                }});
            }}
            
            toggleVertebra(vertebra, visible) {{
                if (this.vertebraMeshes[vertebra]) {{
                    this.vertebraMeshes[vertebra].visible = visible;
                }}
            }}
            
            setOpacity(opacity) {{
                Object.values(this.vertebraMeshes).forEach(mesh => {{
                    mesh.material.opacity = opacity;
                }});
            }}
            
            showPuncturePlan(level) {{
                this.clearPuncturePoints();
                
                if (!level || !this.puncturePoints[level]) {{
                    return;
                }}
                
                const plan = this.puncturePoints[level];
                
                // Create target point A
                if (plan.target_point_A) {{
                    const targetGeometry = new THREE.SphereGeometry(2, 16, 16);
                    const targetMaterial = new THREE.MeshLambertMaterial({{ color: 0xff0000 }});
                    const targetMesh = new THREE.Mesh(targetGeometry, targetMaterial);
                    targetMesh.position.set(...plan.target_point_A);
                    targetMesh.name = 'target_point_A';
                    this.scene.add(targetMesh);
                }}
                
                // Create B points
                const bPointGeometry = new THREE.SphereGeometry(1.5, 12, 12);
                const bPointMaterial = new THREE.MeshLambertMaterial({{ color: 0x00ff00 }});
                
                ['upper_vertebra', 'lower_vertebra'].forEach(vertebra => {{
                    const vertebraData = plan[vertebra];
                    if (vertebraData) {{
                        ['left_articular_tip_B', 'right_articular_tip_B'].forEach(tip => {{
                            if (vertebraData[tip]) {{
                                const bMesh = new THREE.Mesh(bPointGeometry, bPointMaterial);
                                bMesh.position.set(...vertebraData[tip]);
                                bMesh.name = `${{vertebra}}_${{tip}}`;
                                this.scene.add(bMesh);
                            }}
                        }});
                    }}
                }});
                
                console.log(`Showing puncture plan for ${{level}}`);
            }}
            
            clearPuncturePoints() {{
                const objectsToRemove = [];
                this.scene.traverse((object) => {{
                    if (object.name && (
                        object.name.includes('target_point') ||
                        object.name.includes('articular_tip') ||
                        object.name.includes('path_')
                    )) {{
                        objectsToRemove.push(object);
                    }}
                }});
                
                objectsToRemove.forEach(object => {{
                    this.scene.remove(object);
                }});
            }}
            
            resetView() {{
                this.camera.position.set(0, 0, 200);
                this.controls.reset();
            }}
            
            onWindowResize() {{
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }}
        }}
        
        // Initialize when page loads
        window.addEventListener('load', function() {{
            const viewer = new SpineViewer();
        }});
    </script>
</body>
</html>'''
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

if __name__ == "__main__":
    create_embedded_threejs_visualization()
