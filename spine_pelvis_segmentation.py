import os
import nibabel as nib
import numpy as np
from totalsegmentator.python_api import totalsegmentator
import argparse

def segment_spine_pelvis(input_path, output_path):
    """
    Segment spine and pelvis from NII.gz file using TotalSegmentator
    """
    # Create temporary directory for full segmentation
    temp_dir = "temp_segmentation"
    os.makedirs(temp_dir, exist_ok=True)
    
    # Run TotalSegmentator
    print("Running TotalSegmentator...")
    try:
        totalsegmentator(
            input_path,
            temp_dir,
            ml=True,
            nr_thr_resamp=1,
            nr_thr_saving=6,
            fast=False,
            nora_tag="None",
            preview=False,
            task="total"
        )
        print("TotalSegmentator completed")
    except Exception as e:
        print(f"Error: {e}")
        # Try simpler version
        print("Trying simpler parameters...")
        totalsegmentator(input_path, temp_dir)
    
    # Find the main segmentation file
    segmentation_file = None
    possible_files = ["segmentations.nii.gz", "temp_segmentation.nii", "segmentation.nii.gz", "output.nii.gz"]
    
    for filename in possible_files:
        file_path = os.path.join(temp_dir, filename)
        if os.path.exists(file_path):
            segmentation_file = file_path
            break
    
    if segmentation_file is None:
        print("No segmentation file found!")
        # List all files in temp directory
        if os.path.exists(temp_dir):
            all_files = os.listdir(temp_dir)
            print(f"Files in {temp_dir}: {all_files}")
        return
    
    print(f"Found segmentation file: {segmentation_file}")
    
    # Load the complete segmentation
    seg_img = nib.load(segmentation_file)
    seg_data = seg_img.get_fdata()
    
    print(f"Segmentation shape: {seg_data.shape}")
    unique_labels = np.unique(seg_data)
    print(f"Unique labels: {sorted(unique_labels)}")
    
    # Load reference image for header info
    ref_img = nib.load(input_path)
    
    # TotalSegmentator label mapping (corrected based on actual output)
    # Based on the actual labels found in the segmentation
    spine_pelvis_mapping = {
        # 根据实际输出的标签进行映射
        # 从大到小的体素数量来推断椎体位置
        20: 20,  # 最大的椎体区域，可能是多个椎体的组合，保持原标签
        21: 21,  # 较小区域，可能是椎间盘或其他结构
        25: 25,  # 大的骨盆区域，可能是髂骨
        26: 26,  # 骨盆区域
        27: 27,  # sacrum - 骶骨，这个我们需要用作S1
        28: 22,  # 根据体素数量推断为L3
        29: 23,  # 根据体素数量推断为L4
        30: 24,  # 根据体素数量推断为L5
        31: 19,  # T12
        32: 18,  # T11
        33: 17,  # T10
        34: 16,  # T9
        35: 15,  # T8
        # 保持其他标签不变
        1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9,
        11: 11, 14: 14, 18: 18, 19: 19,
        51: 51, 52: 52, 63: 63, 64: 64, 65: 65, 66: 66, 67: 67, 68: 68,
        77: 77, 78: 78, 79: 79, 80: 80, 81: 81, 82: 82, 83: 83, 84: 84,
        85: 85, 86: 86, 87: 87, 88: 88, 89: 89, 98: 98, 99: 99, 100: 100,
        101: 101, 102: 102, 103: 103, 110: 110, 111: 111, 112: 112,
        113: 113, 114: 114, 115: 115, 117: 117
    }
    
    # Create output segmentation
    output_data = np.zeros(ref_img.shape, dtype=np.uint8)
    
    # Map labels
    found_labels = []
    for orig_label, new_label in spine_pelvis_mapping.items():
        if orig_label in unique_labels:
            mask = seg_data == orig_label
            if np.any(mask):
                output_data[mask] = new_label
                found_labels.append(orig_label)
    
    print(f"Found and mapped {len(found_labels)} labels: {found_labels}")
    
    # Save merged segmentation
    output_img = nib.Nifti1Image(output_data, ref_img.affine, ref_img.header)
    nib.save(output_img, output_path)
    
    # Clean up temporary files (commented out to preserve temp files for debugging)
    # import shutil
    # shutil.rmtree(temp_dir)
    
    print(f"Segmentation saved to: {output_path}")
    print("Label mapping:")
    spine_labels = [
        "vertebrae_C1", "vertebrae_C2", "vertebrae_C3", "vertebrae_C4", 
        "vertebrae_C5", "vertebrae_C6", "vertebrae_C7",
        "vertebrae_T1", "vertebrae_T2", "vertebrae_T3", "vertebrae_T4",
        "vertebrae_T5", "vertebrae_T6", "vertebrae_T7", "vertebrae_T8",
        "vertebrae_T9", "vertebrae_T10", "vertebrae_T11", "vertebrae_T12",
        "vertebrae_L1", "vertebrae_L2", "vertebrae_L3", "vertebrae_L4", "vertebrae_L5"
    ]
    pelvis_labels = ["hip_left", "hip_right", "sacrum"]
    
    for i, label in enumerate(spine_labels, 1):
        print(f"  {i}: {label}")
    for i, label in enumerate(pelvis_labels, 25):
        print(f"  {i}: {label}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Segment spine and pelvis using TotalSegmentator")
    parser.add_argument("input", help="Input NII.gz file path")
    parser.add_argument("output", help="Output segmentation NII.gz file path")
    
    args = parser.parse_args()
    
    segment_spine_pelvis(args.input, args.output)

